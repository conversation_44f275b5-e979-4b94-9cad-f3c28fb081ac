package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.text.Html;

import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.R;
import com.dep.biguo.bean.BiguoVipBean;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.databinding.BiguoVipActivityBinding;
import com.dep.biguo.di.component.DaggerBiguoVipComponent;
import com.dep.biguo.dialog.RuleDialog;
import com.dep.biguo.mvp.contract.BiguoVipContract;
import com.dep.biguo.mvp.presenter.BiguoVipPresenter;
import com.dep.biguo.mvp.ui.adapter.BiguoVipAdapter;
import com.dep.biguo.mvp.ui.adapter.VipTableSimpleAdapter;
import com.dep.biguo.util.VipTableDataProvider;
import com.biguo.utils.util.TintDrawableUtil;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.image.TextDrawableLoader;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.widget.ToolBar;
import com.jess.arms.di.component.AppComponent;

import java.util.List;
import java.util.Locale;

public class BiguoVipActivity extends BaseLoadSirActivity<BiguoVipPresenter> implements BiguoVipContract.View,View.OnClickListener {
    private BiguoVipActivityBinding binding;

    private BiguoVipAdapter welfareAdapter; // 不再使用列表卡片，保留字段以兼容旧代码
    private ToolBar toolBar;

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerBiguoVipComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_activity);
        binding.setOnClickListener(this);
        toolBar = new ToolBar.Builder(this, (ViewGroup) binding.scrollView.getParent())
                .setTarget(ToolBar.Builder.LEFT)
                .setDrawablesRes(new int[]{R.drawable.arrow_white_back, 0, 0, 0})
                .setTarget(ToolBar.Builder.TITLE)
                .setText("笔果专享折扣卡")
                .setTextColor(R.color.twhite)
                .build()
                .setFollowScrollBackground(binding.scrollView, (toolBar1, effectiveRange, changeRate) -> {
                    //计算图标的颜色，夜间模式,图标只需要白色
                    int iconRgb = Math.max(102, 255 - (int) ((UserCache.isDayNight() ? 0 : changeRate) * 255));
                    //计算图标的颜色，夜间模式,文字只需要白色
                    int textRgb = Math.max(51, 255 - (int) ((UserCache.isDayNight() ? 0 : changeRate) * 255));

                    setToolBarIconColor(Color.rgb(iconRgb, iconRgb, iconRgb), Color.rgb(textRgb, textRgb, textRgb));
                });

        UserBean userBean = UserCache.getUserCache();
        android.widget.ImageView ivAvatar = findViewById(R.id.iv_avatar);
        TextView tvNickname = findViewById(R.id.tv_nickname);
        if (ivAvatar != null) {
            ImageLoader.loadAvatar(ivAvatar, userBean.getAvatar());
        }
        if (tvNickname != null) {
            tvNickname.setText(userBean.getNickname());
        }

        // 初始化新的权益表格（按设计图）
        setupVipTable();

        // 成功页不允许再切换，默认禁用交互
        lockTopCardInteraction();
        // 启动调试过期计时（若设置了10秒后过期）
        startExpireCountdownIfNeeded();
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.scrollView;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onRequest() {
        mPresenter.getData(true);
    }

    /**给标题栏的图标着色
     * @param color 图标的颜色
     */
    public void setToolBarIconColor(int color, int textColor){
        TextView leftView = toolBar.getViewByTarget(ToolBar.Builder.LEFT);
        TintDrawableUtil.StartTintDrawable(leftView, color);

        toolBar.getViewByTarget(ToolBar.Builder.TITLE).setTextColor(textColor);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mPresenter !=null && mPresenter.getBiguoVipBean() != null) {
            mPresenter.getData(false);
        }
        // 回到页面时再次校验，防止在后台跨过过期点
        startExpireCountdownIfNeeded();
    }

    @Override
    public void getDataSuccess(BiguoVipBean bean) {
        // 根据购买类型切换“整列背景”与列容器样式（与开通页一致）
        boolean isSuperVip = false;
        try {
            UserBean userBean = UserCache.getUserCache();
            isSuperVip = userBean != null && "1".equals(userBean.getIs_super_vip());
        } catch (Exception ignore) {}
        updateTableHeaderBackground(isSuperVip);
        updateTopCardTextColors(isSuperVip);
        lockTopCardInteraction();

        if(UserCache.isMemberShip()) {//已开通会员
            // 头部右侧徽标与按钮在新设计中不使用，移除老控件逻辑
            // 修改 vipTitleView 文案：当前已享受折扣会员卡特权（到期时间：yyyy-MM-dd）
            try {
                String title = "当前已享受折扣会员卡特权";
                String date = bean.getExpire_date();
                if (date == null) date = "--";
                String html = String.format(Locale.CHINA, "%s <font color='#999999'>（到期时间：%s）</font>", title, date);
                binding.vipTitleView.setText(Html.fromHtml(html));
            } catch (Exception ignore) {}

        }else {//未开通会员
            // 未开通时保持默认样式
            try {
                String html = "当前已享受折扣会员卡特权 <font color='#999999'>（到期时间：--）</font>";
                binding.vipTitleView.setText(Html.fromHtml(html));
            } catch (Exception ignore) {}
        }
        // 新表格使用静态数据提供者展示，不再使用旧的 Recycler 列表

        try {
            TextView tvCourseDiscount = findViewById(R.id.tv_course_discount);
            if (tvCourseDiscount != null) {
                tvCourseDiscount.setText(String.format(Locale.CHINA,"课程折扣%.1f折", Float.parseFloat(bean.getDiscount()) * 10));
            }
        }catch (Exception e){
            TextView tvCourseDiscount = findViewById(R.id.tv_course_discount);
            if (tvCourseDiscount != null) tvCourseDiscount.setText("课程折扣10折");
        }
    }

    @Override
    public void onClick(View view){
        // 新设计暂无点击控件的回调需求
    }

    @Override
    public void showPayDialog(List<DiscountBean> allDiscount) {
        new DiscountPayDialog.Builder(this, PayUtils.MEMBERSHIP)
                .setGoodsName("笔果折扣卡")
                .setShowGuobi(false)
                .setPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setPayPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setDiscountList(allDiscount)
                .setOnPayListener((joinGroup, discount, payType) -> {
                    int coupon_id = discount == null ? 0 : discount.getId();
                    mPresenter.payOrder(payType, 0, coupon_id);
                })
                .build()
                .show();
    }

    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    public void paySuccess() {
        showMessage("支付成功");
        mPresenter.getData(false);
    }

    @Override
    public void getRuleSuccess(List<String> rule) {
        new RuleDialog(this)
                .setTitleText("使用须知")
                .setRules(rule)
                .show();
    }

    // ================== 本地调试：10秒后过期 ==================
    private static final String PREF_VIP_DEBUG = "vip_debug_prefs";
    private static final String KEY_EXPIRE_AT_MS = "expire_at_ms";
    private Runnable expireRunnable;

    private long getDebugExpireAtMs() {
        SharedPreferences sp = getSharedPreferences(PREF_VIP_DEBUG, Context.MODE_PRIVATE);
        return sp.getLong(KEY_EXPIRE_AT_MS, 0L);
    }

    private void clearDebugExpireAt() {
        SharedPreferences sp = getSharedPreferences(PREF_VIP_DEBUG, Context.MODE_PRIVATE);
        sp.edit().remove(KEY_EXPIRE_AT_MS).apply();
    }

    private void startExpireCountdownIfNeeded() {
        long expireAt = getDebugExpireAtMs();
        if (expireAt <= 0) return;
        long now = System.currentTimeMillis();
        long delay = expireAt - now;
        if (delay <= 0) {
            navigateToExpired();
            return;
        }
        // 显示剩余秒数提示
        TextView tv = findViewById(R.id.tv_debug_expire_countdown);
        if (tv != null) {
            tv.setVisibility(View.VISIBLE);
            long seconds = (delay + 999) / 1000;
            tv.setText(String.format(java.util.Locale.CHINA, "剩余 %d 秒自动过期", seconds));
        }
        if (expireRunnable != null) {
            binding.getRoot().removeCallbacks(expireRunnable);
        }
        expireRunnable = new Runnable() {
            @Override
            public void run() {
                long left = getDebugExpireAtMs() - System.currentTimeMillis();
                if (left <= 0) {
                    navigateToExpired();
                    return;
                }
                TextView tv = findViewById(R.id.tv_debug_expire_countdown);
                if (tv != null) {
                    long secs = (left + 999) / 1000;
                    tv.setText(String.format(java.util.Locale.CHINA, "剩余 %d 秒自动过期", secs));
                }
                binding.getRoot().postDelayed(this, 1000L);
            }
        };
        binding.getRoot().post(expireRunnable);
    }

    private void navigateToExpired() {
        try {
            // 标记为过期
            UserBean userBean = UserCache.getUserCache();
            if (userBean != null) {
                userBean.setMembership(0);
                UserCache.cacheUser(userBean);
            }
            clearDebugExpireAt();
        } catch (Exception ignore) {}
        com.jess.arms.utils.ArmsUtils.startActivity(BiguoVipExpiredActivity.class);
        finish();
    }

    /**
     * 初始化“会员卡权益”表格：特权/超级会员卡/会员卡/普通用户 四列
     */
    private void setupVipTable(){
        androidx.recyclerview.widget.RecyclerView rvPrivilege = findViewById(R.id.recycler_privilege);
        androidx.recyclerview.widget.RecyclerView rvSuper = findViewById(R.id.recycler_super_vip);
        androidx.recyclerview.widget.RecyclerView rvVip = findViewById(R.id.recycler_vip);
        androidx.recyclerview.widget.RecyclerView rvRegular = findViewById(R.id.recycler_regular);

        if (rvPrivilege == null || rvVip == null || rvRegular == null) return;

        rvPrivilege.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        if (rvSuper != null) rvSuper.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvVip.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvRegular.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));

        rvPrivilege.setAdapter(new VipTableSimpleAdapter(mapToPrivilegeItems(VipTableDataProvider.providePrivilegeList())));
        if (rvSuper != null) rvSuper.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideSuperVipList()));
        rvVip.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideVipList()));
        rvRegular.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideRegularList()));

        // 同步顶部卡片三角/麦穗显示：默认按用户是否超级会员
        View leftGrainSuper = findViewById(R.id.leftGrainSuper);
        View rightGrainSuper = findViewById(R.id.rightGrainSuper);
        View leftGrainDiscount = findViewById(R.id.leftGrainDiscount);
        View rightGrainDiscount = findViewById(R.id.rightGrainDiscount);
        View triangleSuper = findViewById(R.id.triangleSuper);
        View triangleDiscount = findViewById(R.id.triangleDiscount);

        boolean isSuperVip = false;
        try {
            UserBean userBean = UserCache.getUserCache();
            isSuperVip = userBean != null && "1".equals(userBean.getIs_super_vip());
        } catch (Exception ignore) {}

        if (leftGrainSuper != null && rightGrainSuper != null && leftGrainDiscount != null && rightGrainDiscount != null && triangleSuper != null && triangleDiscount != null) {
            if (isSuperVip) {
                leftGrainSuper.setVisibility(View.VISIBLE);
                rightGrainSuper.setVisibility(View.VISIBLE);
                triangleSuper.setVisibility(View.VISIBLE);
                leftGrainDiscount.setVisibility(View.INVISIBLE);
                rightGrainDiscount.setVisibility(View.INVISIBLE);
                triangleDiscount.setVisibility(View.INVISIBLE);
            } else {
                leftGrainSuper.setVisibility(View.INVISIBLE);
                rightGrainSuper.setVisibility(View.INVISIBLE);
                triangleSuper.setVisibility(View.INVISIBLE);
                leftGrainDiscount.setVisibility(View.VISIBLE);
                rightGrainDiscount.setVisibility(View.VISIBLE);
                triangleDiscount.setVisibility(View.VISIBLE);
            }
        }
    }

    private java.util.List<VipTableSimpleAdapter.CellItem> mapToPrivilegeItems(java.util.List<String> titles){
        java.util.List<VipTableSimpleAdapter.CellItem> list = new java.util.ArrayList<>();
        for (String t: titles){
            list.add(VipTableSimpleAdapter.CellItem.text(t, Color.parseColor("#333333")));
        }
        return list;
    }

    /**
     * 与 BiguoVipOpenActivity 的 updateTableHeaderBackground 同步的逻辑：
     * 通过背板 View 切换整列底色，并同步列容器边框/表头样式
     */
    private void updateTableHeaderBackground(boolean isSuperVipSelected) {
        // 叠加的背景遮罩视图（整列底色）：黄色与红色
        View yellowBackground = findViewById(R.id.background_yellow_column);
        View redBackground = findViewById(R.id.background_red_column);

        // 列容器与其表头
        android.widget.LinearLayout superVipColumn = findViewById(R.id.super_vip_column);
        android.widget.LinearLayout vipColumn = findViewById(R.id.vip_column);

        View superHeader = null;
        View vipHeader = null;
        if (superVipColumn != null && superVipColumn.getChildCount() > 0) {
            superHeader = superVipColumn.getChildAt(0);
        }
        if (vipColumn != null && vipColumn.getChildCount() > 0) {
            vipHeader = vipColumn.getChildAt(0);
        }

        if (isSuperVipSelected) {
            if (yellowBackground != null) yellowBackground.setVisibility(View.VISIBLE);
            if (redBackground != null) redBackground.setVisibility(View.GONE);

            if (vipColumn != null) vipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);

            if (superVipColumn != null) superVipColumn.setBackgroundColor(Color.TRANSPARENT);
            if (superHeader != null) superHeader.setBackgroundColor(Color.TRANSPARENT);
        } else {
            if (yellowBackground != null) yellowBackground.setVisibility(View.GONE);
            if (redBackground != null) redBackground.setVisibility(View.VISIBLE);

            if (vipColumn != null) vipColumn.setBackgroundColor(Color.TRANSPARENT);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);

            if (superVipColumn != null) superVipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (superHeader != null) superHeader.setBackgroundResource(R.drawable.bg_super_vip_header);
        }
    }

    /**
     * 顶部卡片选中联动：装饰与整列表格背景一起切换
     */
    private void selectTopCard(boolean isSuperVipSelected) {
        // 1) 装饰同步（麦穗、三角）
        View leftGrainSuper = findViewById(R.id.leftGrainSuper);
        View rightGrainSuper = findViewById(R.id.rightGrainSuper);
        View leftGrainDiscount = findViewById(R.id.leftGrainDiscount);
        View rightGrainDiscount = findViewById(R.id.rightGrainDiscount);
        View triangleSuper = findViewById(R.id.triangleSuper);
        View triangleDiscount = findViewById(R.id.triangleDiscount);

        if (leftGrainSuper != null && rightGrainSuper != null && leftGrainDiscount != null && rightGrainDiscount != null && triangleSuper != null && triangleDiscount != null) {
            if (isSuperVipSelected) {
                leftGrainSuper.setVisibility(View.VISIBLE);
                rightGrainSuper.setVisibility(View.VISIBLE);
                triangleSuper.setVisibility(View.VISIBLE);
                leftGrainDiscount.setVisibility(View.INVISIBLE);
                rightGrainDiscount.setVisibility(View.INVISIBLE);
                triangleDiscount.setVisibility(View.INVISIBLE);
            } else {
                leftGrainSuper.setVisibility(View.INVISIBLE);
                rightGrainSuper.setVisibility(View.INVISIBLE);
                triangleSuper.setVisibility(View.INVISIBLE);
                leftGrainDiscount.setVisibility(View.VISIBLE);
                rightGrainDiscount.setVisibility(View.VISIBLE);
                triangleDiscount.setVisibility(View.VISIBLE);
            }
        }

        // 2) 表格列背景同步
        updateTableHeaderBackground(isSuperVipSelected);
        // 3) 顶部卡片文字颜色同步
        updateTopCardTextColors(isSuperVipSelected);
    }

    private void lockTopCardInteraction(){
        View superVipTitleLayout = findViewById(R.id.superVipTitleLayout);
        View discountTitleLayout = findViewById(R.id.discountTitleLayout);
        if (superVipTitleLayout != null){
            superVipTitleLayout.setClickable(false);
            superVipTitleLayout.setFocusable(false);
            superVipTitleLayout.setOnClickListener(null);
        }
        if (discountTitleLayout != null){
            discountTitleLayout.setClickable(false);
            discountTitleLayout.setFocusable(false);
            discountTitleLayout.setOnClickListener(null);
        }
    }

    /**
     * 顶部卡片标题/副标题文字颜色与开通页保持一致：
     * - 选中超级会员：左侧标题/副标题为 #FFE9A3，右侧为 #FFFFFF
     * - 选中会员卡：右侧标题/副标题为 #FFE9A3，左侧为 #FFFFFF
     */
    private void updateTopCardTextColors(boolean isSuperVipSelected) {
        // 注意：本页顶部卡片来自 item_vip_card_view.xml，ID 与开通页不同
        TextView superVipTitle = findViewById(R.id.superVipTitleText);
        TextView priceAndValidityView = findViewById(R.id.superVipSubText);
        TextView discountView = findViewById(R.id.discountTitleText);
        TextView enjoyCountView = findViewById(R.id.tv_course_discount);

        if (superVipTitle == null || priceAndValidityView == null || discountView == null || enjoyCountView == null) {
            return;
        }

        int gold = Color.parseColor("#FFE9A3");
        int white = Color.parseColor("#FFFFFF");

        if (isSuperVipSelected) {
            superVipTitle.setTextColor(gold);
            priceAndValidityView.setTextColor(gold);
            discountView.setTextColor(white);
            enjoyCountView.setTextColor(white);
        } else {
            superVipTitle.setTextColor(white);
            priceAndValidityView.setTextColor(white);
            discountView.setTextColor(gold);
            enjoyCountView.setTextColor(gold);
        }
    }
}
