package com.dep.biguo.mvp.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.dep.biguo.R;

import java.util.List;

/**
 * Simple VIP table adapter for displaying cell items (text or icon)
 */
public class VipTableSimpleAdapter extends RecyclerView.Adapter<VipTableSimpleAdapter.CellViewHolder> {
    
    private List<CellItem> data;
    
    public VipTableSimpleAdapter(List<CellItem> data) {
        this.data = data;
    }
    
    @NonNull
    @Override
    public CellViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_vip_table_cell_text, parent, false);
        return new CellViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CellViewHolder holder, int position) {
        CellItem item = data.get(position);
        holder.bind(item);
    }
    
    @Override
    public int getItemCount() {
        return data != null ? data.size() : 0;
    }
    
    public void updateData(List<CellItem> newData) {
        this.data = newData;
        notifyDataSetChanged();
    }
    
    static class CellViewHolder extends RecyclerView.ViewHolder {
        
        private TextView textView;
        private ImageView imageView;
        
        public CellViewHolder(@NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.tv);
            imageView = itemView.findViewById(R.id.iv);
        }
        
        public void bind(CellItem item) {
            if (item.isIcon()) {
                // Show icon
                textView.setVisibility(View.GONE);
                imageView.setVisibility(View.VISIBLE);
                imageView.setImageResource(item.getIconRes());
            } else {
                // Show text
                textView.setVisibility(View.VISIBLE);
                imageView.setVisibility(View.GONE);
                textView.setText(item.getText());
                textView.setTextColor(item.getTextColor());
            }
        }
    }
    
    /**
     * Data model for table cell items
     */
    public static class CellItem {
        private String text;
        private int textColor;
        private int iconRes;
        private boolean isIcon;
        
        private CellItem(String text, int textColor) {
            this.text = text;
            this.textColor = textColor;
            this.isIcon = false;
        }
        
        private CellItem(int iconRes) {
            this.iconRes = iconRes;
            this.isIcon = true;
        }
        
        public static CellItem text(String text, int textColor) {
            return new CellItem(text, textColor);
        }
        
        public static CellItem icon(int iconRes) {
            return new CellItem(iconRes);
        }
        
        public String getText() {
            return text;
        }
        
        public int getTextColor() {
            return textColor;
        }
        
        public int getIconRes() {
            return iconRes;
        }
        
        public boolean isIcon() {
            return isIcon;
        }
    }
}
