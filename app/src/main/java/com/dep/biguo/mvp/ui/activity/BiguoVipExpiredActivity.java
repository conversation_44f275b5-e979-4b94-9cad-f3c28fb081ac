package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;
import android.util.Log;

import com.dep.biguo.R;
import com.dep.biguo.databinding.BiguoVipExpiredActivityBinding;
import com.dep.biguo.mvp.ui.adapter.VipTableSimpleAdapter;
import com.dep.biguo.util.VipTableDataProvider;

import java.util.ArrayList;
import java.util.List;

public class BiguoVipExpiredActivity extends AppCompatActivity implements View.OnClickListener {
    private static final String TAG = "VipExpired";
    private BiguoVipExpiredActivityBinding binding;
    private String originalSinglePriceText;
    private String originalGroupPriceText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置状态栏透明，让内容延伸到状态栏区域
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
            window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE | 
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            );
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
        
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_expired_activity);
        Log.d(TAG, "onCreate");

        binding.openGroupVipLayout.setOnClickListener(this);
        binding.serviceAgreementView.setOnClickListener(this);
        View vipInfoLayout = findViewById(R.id.vip_info_layout);
        View discountInfoLayout = findViewById(R.id.discount_info_layout);
        if (vipInfoLayout != null) vipInfoLayout.setOnClickListener(this);
        if (discountInfoLayout != null) discountInfoLayout.setOnClickListener(this);
        binding.singlePurchaseCard.setOnClickListener(this);
        binding.groupPurchaseCard.setOnClickListener(this);

        setupVipTable();
        
        // 设置返回按钮点击事件
        View backButton = findViewById(R.id.backButton);
        if (backButton != null) {
            backButton.setOnClickListener(v -> finish());
        }

        String text = "我已阅读并同意<font color='#FE5656'>会员协议</font>，且知晓权益为一年有效期";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.serviceAgreementView.setText(Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY));
        } else {
            binding.serviceAgreementView.setText(Html.fromHtml(text));
        }

        try {
            TextView singlePriceViewInit = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView groupPriceViewInit = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
            if (singlePriceViewInit != null) originalSinglePriceText = String.valueOf(singlePriceViewInit.getText());
            if (groupPriceViewInit != null) originalGroupPriceText = String.valueOf(groupPriceViewInit.getText());
        } catch (Exception ignore) {}

        TextView singleOriginalPrice = binding.getRoot().findViewById(R.id.singleOriginalPrice);
        TextView groupOriginalPrice = binding.getRoot().findViewById(R.id.groupOriginalPrice);
        if (singleOriginalPrice != null) {
            singleOriginalPrice.setPaintFlags(singleOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }
        if (groupOriginalPrice != null) {
            groupOriginalPrice.setPaintFlags(groupOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }

        View contentArea = binding.getRoot().findViewById(R.id.content_area);
        TextView danDuGouMai = binding.getRoot().findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
        TextView pMoney = binding.getRoot().findViewById(R.id.p_money);
        if (contentArea != null) {
            contentArea.setOnClickListener(v -> {
                int color = Color.parseColor("#FE5656");
                if (danDuGouMai != null) danDuGouMai.setTextColor(color);
                if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(color);
                if (pMoney != null) pMoney.setTextColor(color);

                TextView pinTuanGouMaiOther = binding.getRoot().findViewById(R.id.pingtuangoumai);
                TextView pinTuanGouMaiMoneyOther = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
                TextView ppMoneyOther = binding.getRoot().findViewById(R.id.pp_money);
                int normal = Color.parseColor("#333333");
                if (pinTuanGouMaiOther != null) pinTuanGouMaiOther.setTextColor(normal);
                if (pinTuanGouMaiMoneyOther != null) pinTuanGouMaiMoneyOther.setTextColor(normal);
                if (ppMoneyOther != null) ppMoneyOther.setTextColor(normal);

                View groupPurchaseContentArea2 = binding.getRoot().findViewById(R.id.group_purchase_content_area);
                int delta = dpToPx(20);
                adjustViewWidth(contentArea, +delta);
                adjustViewWidth(groupPurchaseContentArea2, -delta);

                if (binding.contentArea != null) {
                    binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
                }
                if (binding.groupPurchaseContentArea != null) {
                    binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
                }
            });
        }

        View groupPurchaseContentArea = binding.getRoot().findViewById(R.id.group_purchase_content_area);
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);
        if (groupPurchaseContentArea != null) {
            // 默认让拼团购买高亮（红色），左侧为常规色
            int highlightInit = Color.parseColor("#FE5656");
            int normalInit = Color.parseColor("#333333");
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(highlightInit);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(highlightInit);
            if (ppMoney != null) ppMoney.setTextColor(highlightInit);

            TextView danDuGouMaiInit = binding.getRoot().findViewById(R.id.dandugoumai);
            TextView danDuGouMaiMoneyInit = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView pMoneyInit = binding.getRoot().findViewById(R.id.p_money);
            if (danDuGouMaiInit != null) danDuGouMaiInit.setTextColor(normalInit);
            if (danDuGouMaiMoneyInit != null) danDuGouMaiMoneyInit.setTextColor(normalInit);
            if (pMoneyInit != null) pMoneyInit.setTextColor(normalInit);

            // 背景与默认选中样式同步：右侧高亮，左侧常规
            if (binding.groupPurchaseContentArea != null) {
                binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
            }
            if (binding.contentArea != null) {
                binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
            }

            groupPurchaseContentArea.setOnClickListener(v -> {
                int color = Color.parseColor("#FE5656");
                if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(color);
                if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(color);
                if (ppMoney != null) ppMoney.setTextColor(color);

                TextView danDuGouMaiOther2 = binding.getRoot().findViewById(R.id.dandugoumai);
                TextView danDuGouMaiMoneyOther2 = binding.getRoot().findViewById(R.id.dandugoumai_money);
                TextView pMoneyOther2 = binding.getRoot().findViewById(R.id.p_money);
                int normal2 = Color.parseColor("#333333");
                if (danDuGouMaiOther2 != null) danDuGouMaiOther2.setTextColor(normal2);
                if (danDuGouMaiMoneyOther2 != null) danDuGouMaiMoneyOther2.setTextColor(normal2);
                if (pMoneyOther2 != null) pMoneyOther2.setTextColor(normal2);

                View contentArea2 = binding.getRoot().findViewById(R.id.content_area);
                int delta2 = dpToPx(20);
                adjustViewWidth(groupPurchaseContentArea, +delta2);
                adjustViewWidth(contentArea2, -delta2);

                if (binding.groupPurchaseContentArea != null) {
                    binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
                }
                if (binding.contentArea != null) {
                    binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
                }
            });
        }
        
        // 初始化默认选中超级会员卡
        selectTopCard(true);

        // 动态设置顶部padding，确保内容不被状态栏遮挡
        View topBannerContainer = findViewById(R.id.top_banner_container);
        if (topBannerContainer != null) {
            int statusBarHeight = getStatusBarHeight();
            topBannerContainer.setPadding(
                topBannerContainer.getPaddingLeft(),
                statusBarHeight + topBannerContainer.getPaddingTop(),
                topBannerContainer.getPaddingRight(),
                topBannerContainer.getPaddingBottom()
            );
        }

        // 初始化时设置数字颜色
        restoreOriginalCountViewState();

    }

    private void setupVipTable() {
        androidx.recyclerview.widget.RecyclerView rvPrivilege = binding.getRoot().findViewById(R.id.recycler_privilege);
        androidx.recyclerview.widget.RecyclerView rvSuper = binding.getRoot().findViewById(R.id.recycler_super_vip);
        androidx.recyclerview.widget.RecyclerView rvVip = binding.getRoot().findViewById(R.id.recycler_vip);
        androidx.recyclerview.widget.RecyclerView rvRegular = binding.getRoot().findViewById(R.id.recycler_regular);

        rvPrivilege.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvSuper.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvVip.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvRegular.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));

        rvPrivilege.setAdapter(new VipTableSimpleAdapter(mapToPrivilegeItems(VipTableDataProvider.providePrivilegeList())));
        rvSuper.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideSuperVipList()));
        rvVip.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideVipList()));
        rvRegular.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideRegularList()));
    }

    private List<VipTableSimpleAdapter.CellItem> mapToPrivilegeItems(List<String> titles){
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        for (String t: titles){
            list.add(VipTableSimpleAdapter.CellItem.text(t, Color.parseColor("#333333")));
        }
        return list;
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    private void adjustViewWidth(View targetView, int deltaPx) {
        if (targetView == null) return;
        View parent = (View) targetView.getParent();
        if (parent == null) return;

        int parentWidth = parent.getWidth();
        if (parentWidth <= 0) {
            targetView.post(() -> adjustViewWidth(targetView, deltaPx));
            return;
        }

        int currentWidth = targetView.getWidth();
        if (currentWidth <= 0) {
            ViewGroup.LayoutParams lp = targetView.getLayoutParams();
            currentWidth = lp != null && lp.width > 0 ? lp.width : parentWidth;
        }

        int minWidth = Math.max(targetView.getMinimumWidth(), dpToPx(150));
        int newWidth = Math.max(minWidth, Math.min(parentWidth, currentWidth + deltaPx));

        ViewGroup.LayoutParams layoutParams = targetView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = newWidth;
            targetView.setLayoutParams(layoutParams);
            targetView.requestLayout();
            targetView.invalidate();
        }
    }


    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.vip_info_layout) {
            // 选中左侧卡：文字变金色，右侧变白；三角/麦穗显示；整列表格切换为黄色列
            selectTopCard(true);
        } else if (id == R.id.discount_info_layout) {
            // 选中右侧卡：文字变金色，左侧变白；三角/麦穗显示；整列表格切换为红色列
            selectTopCard(false);
        } else if (id == R.id.singlePurchaseCard) {
            updatePurchaseCardWeight(true);
        } else if (id == R.id.groupPurchaseCard) {
            updatePurchaseCardWeight(false);
        }
    }

    private void selectTopCard(boolean isSuperVipSelected) {
        // 麦穗与三角切换
        View leftGrainSuper = findViewById(R.id.leftGrainSuper);
        View rightGrainSuper = findViewById(R.id.rightGrainSuper);
        View triangleSuper = findViewById(R.id.triangleSuper);
        // 右侧麦穗在过期页是占位图，依然配合显示/隐藏
        View leftGrainDiscount = findViewById(R.id.leftGrainDiscount);
        View rightGrainDiscount = findViewById(R.id.rightGrainDiscount);
        View triangleDiscount = findViewById(R.id.triangleDiscount);

        TextView superVipTitle = findViewById(R.id.superVipTitle);
        TextView priceAndValidityView = findViewById(R.id.priceAndValidityView);
        TextView discountView = findViewById(R.id.discountView);
        TextView enjoyCountView = findViewById(R.id.enjoyCountView);

        int gold = Color.parseColor("#FFE9A3");
        int gray = Color.parseColor("#999999");

        // 更新购买卡片的价格
        TextView singlePriceView = findViewById(R.id.dandugoumai_money);
        TextView groupPriceView = findViewById(R.id.pingtuangoumai_money);
        TextView singleOriginalPrice = findViewById(R.id.singleOriginalPrice);
        TextView groupOriginalPrice = findViewById(R.id.groupOriginalPrice);
        
        // 获取"立省"文本视图（它们是 content_area 和 group_purchase_content_area 的最后一个子视图）
        View contentArea = findViewById(R.id.content_area);
        View groupContentArea = findViewById(R.id.group_purchase_content_area);
        TextView singleSaveText = null;
        TextView groupSaveText = null;
        
        if (contentArea instanceof ViewGroup) {
            ViewGroup vg = (ViewGroup) contentArea;
            if (vg.getChildCount() > 4) {
                View lastChild = vg.getChildAt(4);
                if (lastChild instanceof TextView) {
                    singleSaveText = (TextView) lastChild;
                }
            }
        }
        
        if (groupContentArea instanceof ViewGroup) {
            ViewGroup vg = (ViewGroup) groupContentArea;
            if (vg.getChildCount() > 4) {
                View lastChild = vg.getChildAt(4);
                if (lastChild instanceof TextView) {
                    groupSaveText = (TextView) lastChild;
                }
            }
        }

        if (isSuperVipSelected) {
            // 选中超级会员卡
            if (superVipTitle != null) superVipTitle.setTextColor(gold);
            if (priceAndValidityView != null) priceAndValidityView.setTextColor(gold);
            if (discountView != null) discountView.setTextColor(gray);
            if (enjoyCountView != null) enjoyCountView.setTextColor(gray);

            if (leftGrainSuper != null) leftGrainSuper.setVisibility(View.VISIBLE);
            if (rightGrainSuper != null) rightGrainSuper.setVisibility(View.VISIBLE);
            if (triangleSuper != null) triangleSuper.setVisibility(View.VISIBLE);
            if (leftGrainDiscount != null) leftGrainDiscount.setVisibility(View.INVISIBLE);
            if (rightGrainDiscount != null) rightGrainDiscount.setVisibility(View.INVISIBLE);
            if (triangleDiscount != null) triangleDiscount.setVisibility(View.INVISIBLE);

            // 更新价格为超级会员价格
            if (singlePriceView != null) singlePriceView.setText("17.9");
            if (groupPriceView != null) groupPriceView.setText("14.9");
            if (singleOriginalPrice != null) singleOriginalPrice.setText("原价¥19.9");
            if (groupOriginalPrice != null) groupOriginalPrice.setText("原价¥19.9");
            if (singleSaveText != null) singleSaveText.setText("立省¥2");
            if (groupSaveText != null) groupSaveText.setText("立省¥5");

            updateTableHeaderBackground(true);

            // 恢复底部按钮原始状态
            restoreOriginalButtonState();

            // 恢复权益标题原始状态
            restoreOriginalCountViewState();
        } else {
            // 选中普通会员卡
            if (superVipTitle != null) superVipTitle.setTextColor(gray);
            if (priceAndValidityView != null) priceAndValidityView.setTextColor(gray);
            if (discountView != null) discountView.setTextColor(gold);
            if (enjoyCountView != null) enjoyCountView.setTextColor(gold);

            if (leftGrainSuper != null) leftGrainSuper.setVisibility(View.INVISIBLE);
            if (rightGrainSuper != null) rightGrainSuper.setVisibility(View.INVISIBLE);
            if (triangleSuper != null) triangleSuper.setVisibility(View.INVISIBLE);
            if (leftGrainDiscount != null) leftGrainDiscount.setVisibility(View.VISIBLE);
            if (rightGrainDiscount != null) rightGrainDiscount.setVisibility(View.VISIBLE);
            if (triangleDiscount != null) triangleDiscount.setVisibility(View.VISIBLE);

            // 更新价格为普通会员价格
            if (singlePriceView != null) singlePriceView.setText("12.9");
            if (groupPriceView != null) groupPriceView.setText("9.9");
            if (singleOriginalPrice != null) singleOriginalPrice.setText("原价¥14.9");
            if (groupOriginalPrice != null) groupOriginalPrice.setText("原价¥14.9");
            if (singleSaveText != null) singleSaveText.setText("立省¥2");
            if (groupSaveText != null) groupSaveText.setText("立省¥5");

            // 更新底部按钮文案和显示
            TextView openGroupVipView = findViewById(R.id.openGroupVipView);
            TextView openGroupSubView = findViewById(R.id.openGroupSubView);
            if (openGroupVipView != null) {
                openGroupVipView.setText("¥5.90 立即购买");
            }
            if (openGroupSubView != null) {
                openGroupSubView.setVisibility(View.GONE);
            }

            // 更新权益标题文案和数字颜色
            TextView openCountView = findViewById(R.id.openCountView);
            if (openCountView != null) {
                String text = "折扣会员卡享受6大权益";
                SpannableString spannableString = new SpannableString(text);
                // 将数字"6"设置为红色
                int start = text.indexOf("6");
                if (start != -1) {
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#D53E43")),
                                          start, start + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                openCountView.setText(spannableString);
            }

            updateTableHeaderBackground(false);
        }
    }

    private void updateTableHeaderBackground(boolean isSuperVipSelected) {
        // 叠加的背景遮罩视图（整列底色）：黄色与红色
        View yellowBackground = findViewById(R.id.background_yellow_column);
        View redBackground = findViewById(R.id.background_red_column);

        // 列容器与其表头
        android.widget.LinearLayout superVipColumn = findViewById(R.id.super_vip_column);
        android.widget.LinearLayout vipColumn = findViewById(R.id.vip_column);

        View superHeader = null;
        View vipHeader = null;
        if (superVipColumn != null && superVipColumn.getChildCount() > 0) {
            superHeader = superVipColumn.getChildAt(0);
        }
        if (vipColumn != null && vipColumn.getChildCount() > 0) {
            vipHeader = vipColumn.getChildAt(0);
        }

        if (isSuperVipSelected) {
            if (yellowBackground != null) yellowBackground.setVisibility(View.VISIBLE);
            if (redBackground != null) redBackground.setVisibility(View.GONE);

            if (vipColumn != null) vipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);

            if (superVipColumn != null) superVipColumn.setBackgroundColor(Color.TRANSPARENT);
            if (superHeader != null) superHeader.setBackgroundColor(Color.TRANSPARENT);
        } else {
            if (yellowBackground != null) yellowBackground.setVisibility(View.GONE);
            if (redBackground != null) redBackground.setVisibility(View.VISIBLE);

            if (vipColumn != null) vipColumn.setBackgroundColor(Color.TRANSPARENT);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);

            if (superVipColumn != null) superVipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (superHeader != null) superHeader.setBackgroundResource(R.drawable.bg_super_vip_header);
        }
    }

    private void updatePurchaseCardWeight(boolean isSinglePurchaseSelected) {
        final float targetWeight = 1.0f;
        final float normalWeight = 1.0f;

        android.widget.LinearLayout.LayoutParams singleParams = (android.widget.LinearLayout.LayoutParams) binding.singlePurchaseCard.getLayoutParams();
        android.widget.LinearLayout.LayoutParams groupParams = (android.widget.LinearLayout.LayoutParams) binding.groupPurchaseCard.getLayoutParams();

        float singleEndWeight = isSinglePurchaseSelected ? targetWeight : normalWeight;
        float groupEndWeight = isSinglePurchaseSelected ? normalWeight : targetWeight;

        singleParams.weight = singleEndWeight;
        groupParams.weight = groupEndWeight;
        binding.singlePurchaseCard.setLayoutParams(singleParams);
        binding.groupPurchaseCard.setLayoutParams(groupParams);

        // 文字颜色与背景同步
        TextView danDuGouMai = findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = findViewById(R.id.dandugoumai_money);
        TextView pMoney = findViewById(R.id.p_money);
        TextView pinTuanGouMai = findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = findViewById(R.id.pp_money);

        int highlight = Color.parseColor("#FE5656");
        int normal = Color.parseColor("#333333");
        if (isSinglePurchaseSelected) {
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);

            if (danDuGouMai != null) danDuGouMai.setTextColor(highlight);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(highlight);
            if (pMoney != null) pMoney.setTextColor(highlight);

            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(normal);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(normal);
            if (ppMoney != null) ppMoney.setTextColor(normal);
        } else {
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);

            if (danDuGouMai != null) danDuGouMai.setTextColor(normal);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(normal);
            if (pMoney != null) pMoney.setTextColor(normal);

            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(highlight);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(highlight);
            if (ppMoney != null) ppMoney.setTextColor(highlight);
        }
    }
    
    private int getStatusBarHeight() {
        int result = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    /**
     * 恢复底部按钮原始状态
     */
    private void restoreOriginalButtonState() {
        TextView openGroupVipView = findViewById(R.id.openGroupVipView);
        TextView openGroupSubView = findViewById(R.id.openGroupSubView);
        if (openGroupVipView != null) {
            openGroupVipView.setText("¥12.9 立即拼团");
        }
        if (openGroupSubView != null) {
            openGroupSubView.setText("邀请1名新用户");
            openGroupSubView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 恢复权益标题原始状态
     */
    private void restoreOriginalCountViewState() {
        TextView openCountView = findViewById(R.id.openCountView);
        if (openCountView != null) {
            String text = "超级折扣会员卡享受8大权益";
            SpannableString spannableString = new SpannableString(text);
            // 将数字"8"设置为红色
            int start = text.indexOf("8");
            if (start != -1) {
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#D53E43")),
                                      start, start + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            openCountView.setText(spannableString);
        }
    }
}


