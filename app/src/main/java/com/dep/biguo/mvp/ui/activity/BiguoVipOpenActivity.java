package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.animation.ValueAnimator;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.content.Intent;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.hjq.toast.ToastUtils;
import android.view.View;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.util.Log;

import com.biguo.utils.dialog.MessageDialog;
import com.jess.arms.base.BaseActivity;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.BiguoVipOpenBean;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.TableRowData;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.BiguoVipOpenActivityBinding;
import com.dep.biguo.di.component.DaggerBiguoVipOpenComponent;
import com.dep.biguo.dialog.RuleDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.BiguoVipOpenContract;
import com.dep.biguo.mvp.presenter.BiguoVipOpenPresenter;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.adapter.VipTableAdapter;
import com.dep.biguo.mvp.ui.adapter.VipTableSimpleAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.util.VipTableDataProvider;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.dep.biguo.app.EventBusTags;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

public class BiguoVipOpenActivity extends BaseActivity<BiguoVipOpenPresenter> implements BiguoVipOpenContract.View,View.OnClickListener {
    private static final String TAG = "VipOpen";
    private BiguoVipOpenActivityBinding binding;
    private NormalToolbarUtil toolbarUtil;
    private String originalSinglePriceText;
    private String originalGroupPriceText;
    private boolean isSuperVipSelected = true; // 默认选中超级会员
    private boolean isPurchaseComplete = false; // 新增标志位，用于锁定购买后的UI
    // 过期卡片容器已移除，不再需要调试开关
    // private static final boolean DEBUG_MODE_MOCK_EXPIRED_VIP = true;


    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerBiguoVipOpenComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_open_activity);
        Log.d(TAG, "initView");

        // 根据会员状态路由：
        // - 会员且未过期 => 跳转到已开通会员页
        // - 会员过期 => 跳转到过期会员购买页
        if (routeByMembershipIfNeeded()) {
            return 0;
        }

        // Set up click listeners manually
        binding.openGroupVipLayout.setOnClickListener(this);
        binding.serviceAgreementView.setOnClickListener(this);
        binding.ruleView.setOnClickListener(this);
        // 顶部两块区域统一通过根视图 id 绑定，避免找不到点击
        View vipInfoLayout = findViewById(R.id.vip_info_layout);
        View discountInfoLayout = findViewById(R.id.discount_info_layout);
        if (vipInfoLayout != null) vipInfoLayout.setOnClickListener(this);
        if (discountInfoLayout != null) discountInfoLayout.setOnClickListener(this);
        binding.singlePurchaseCard.setOnClickListener(this);
        binding.groupPurchaseCard.setOnClickListener(this);

        // Set up VIP table RecyclerView
        setupVipTable();

        toolbarUtil = new NormalToolbarUtil(this)
                .setLeftDrawableRes(R.drawable.arrow_white_back)
                .setMustDay(this, true)
                .setAnchorView(binding.ruleView)
                .setCenterTextColor(Color.WHITE);
        toolbarUtil.setFollowScrollListener(binding.nestedScrollView, (toolbar, changeRate, isDay) -> {
                    //计算图标的颜色，深色模式,图标只需要白色
                    int iconRgb = Math.max(102, 255 - (int) ((changeRate) * 255));
                    int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

                    //计算文字的颜色，深色模式,文字只需要白色 
                    int textRgb = Math.max(51, 255 - (int) ((changeRate) * 255));
                    int textColor = Color.argb(255, textRgb, textRgb, textRgb);

                    setIconAndTextAndBackgroundColor(iconColor, textColor);
                });

        String text = "我已阅读并同意<font color='#FE5656'>会员协议</font>，且知晓权益为一年有效期";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.serviceAgreementView.setText(Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY));
        } else {
            binding.serviceAgreementView.setText(Html.fromHtml(text));
        }

        updateCardSelection(true);
        Log.d(TAG, "default select: superVip=true");

        // 记录初始价格文本，便于从折扣价恢复
        try {
            TextView singlePriceViewInit = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView groupPriceViewInit = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
            if (singlePriceViewInit != null) originalSinglePriceText = String.valueOf(singlePriceViewInit.getText());
            if (groupPriceViewInit != null) originalGroupPriceText = String.valueOf(groupPriceViewInit.getText());
        } catch (Exception ignore) {}

        // 无需记录基线宽度，使用固定的最小阈值避免无限变小

        // 强制确保删除线在运行时生效（有些系统版本对 xml 的 paintFlags 兼容性不一致）
        TextView singleOriginalPrice = binding.getRoot().findViewById(R.id.singleOriginalPrice);
        TextView groupOriginalPrice = binding.getRoot().findViewById(R.id.groupOriginalPrice);
        if (singleOriginalPrice != null) {
            singleOriginalPrice.setPaintFlags(singleOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }
        if (groupOriginalPrice != null) {
            groupOriginalPrice.setPaintFlags(groupOriginalPrice.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
        }

        // 点击变色：content_area
        View contentArea = binding.getRoot().findViewById(R.id.content_area);
        TextView danDuGouMai = binding.getRoot().findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
        TextView pMoney = binding.getRoot().findViewById(R.id.p_money);
        if (contentArea != null) {
            contentArea.setOnClickListener(v -> {
                Log.d(TAG, "click content_area (single)");
                int color = Color.parseColor("#FE5656");
                if (danDuGouMai != null) danDuGouMai.setTextColor(color);
                if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(color);
                if (pMoney != null) pMoney.setTextColor(color);

                // 未选中侧（拼团）字体恢复为默认色
                TextView pinTuanGouMaiOther = binding.getRoot().findViewById(R.id.pingtuangoumai);
                TextView pinTuanGouMaiMoneyOther = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
                TextView ppMoneyOther = binding.getRoot().findViewById(R.id.pp_money);
                int normal = Color.parseColor("#333333");
                if (pinTuanGouMaiOther != null) pinTuanGouMaiOther.setTextColor(normal);
                if (pinTuanGouMaiMoneyOther != null) pinTuanGouMaiMoneyOther.setTextColor(normal);
                if (ppMoneyOther != null) ppMoneyOther.setTextColor(normal);

                // 宽度联动：content_area +10dp，group_purchase_content_area -10dp（支持未测量时的兜底与延后）
                View groupPurchaseContentArea2 = binding.getRoot().findViewById(R.id.group_purchase_content_area);
                int delta = dpToPx(20);
                adjustViewWidth(contentArea, +delta);
                adjustViewWidth(groupPurchaseContentArea2, -delta);

                // 背景跟随：被增宽的一侧使用渐变黄，另一侧使用纯黄
                if (binding.contentArea != null) {
                    binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
                }
                if (binding.groupPurchaseContentArea != null) {
                    binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
                }

                // 不弹出购买弹窗
            });
        }

        // 点击变色：group_purchase_content_area
        View groupPurchaseContentArea = binding.getRoot().findViewById(R.id.group_purchase_content_area);
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);
        if (groupPurchaseContentArea != null) {
            // 初始不高亮（未选中侧使用默认 #333333）
            int normalInit = Color.parseColor("#333333");
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(normalInit);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(normalInit);
            if (ppMoney != null) ppMoney.setTextColor(normalInit);

            groupPurchaseContentArea.setOnClickListener(v -> {
                Log.d(TAG, "click group_purchase_content_area (group)");
                int color = Color.parseColor("#FE5656");
                // 选中侧（拼团）三行文字高亮
                if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(color);
                if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(color);
                if (ppMoney != null) ppMoney.setTextColor(color);

                // 未选中侧（单独购买）字体恢复为默认色
                TextView danDuGouMaiOther = binding.getRoot().findViewById(R.id.dandugoumai);
                TextView danDuGouMaiMoneyOther = binding.getRoot().findViewById(R.id.dandugoumai_money);
                TextView pMoneyOther = binding.getRoot().findViewById(R.id.p_money);
                int normal2 = Color.parseColor("#333333");
                if (danDuGouMaiOther != null) danDuGouMaiOther.setTextColor(normal2);
                if (danDuGouMaiMoneyOther != null) danDuGouMaiMoneyOther.setTextColor(normal2);
                if (pMoneyOther != null) pMoneyOther.setTextColor(normal2);

                // 宽度联动：group_purchase_content_area +10dp，content_area -10dp（支持未测量时的兜底与延后）
                View contentArea2 = binding.getRoot().findViewById(R.id.content_area);
                int delta2 = dpToPx(20);
                adjustViewWidth(groupPurchaseContentArea, +delta2);
                adjustViewWidth(contentArea2, -delta2);

                // 背景跟随：被增宽的一侧使用渐变黄，另一侧使用纯黄
                if (binding.groupPurchaseContentArea != null) {
                    binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
                }
                if (binding.contentArea != null) {
                    binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
                }

                // 不弹出购买弹窗
            });
        }

        // 当离开页面时恢复（示例：onPause 恢复，可根据你的需求选择 onStop/onDestroy）


        return 0;
    }

    // 顶部卡片点击在 xml 已绑到 vip_info_layout / discount_info_layout，这里已有 onClick 分支

    private void setupVipTable() {
        androidx.recyclerview.widget.RecyclerView rvPrivilege = binding.getRoot().findViewById(R.id.recycler_privilege);
        androidx.recyclerview.widget.RecyclerView rvSuper = binding.getRoot().findViewById(R.id.recycler_super_vip);
        androidx.recyclerview.widget.RecyclerView rvVip = binding.getRoot().findViewById(R.id.recycler_vip);
        androidx.recyclerview.widget.RecyclerView rvRegular = binding.getRoot().findViewById(R.id.recycler_regular);

        rvPrivilege.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvSuper.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvVip.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));
        rvRegular.setLayoutManager(new androidx.recyclerview.widget.LinearLayoutManager(this));

        rvPrivilege.setAdapter(new VipTableSimpleAdapter(mapToPrivilegeItems(VipTableDataProvider.providePrivilegeList())));
        rvSuper.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideSuperVipList()));
        rvVip.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideVipList()));
        rvRegular.setAdapter(new VipTableSimpleAdapter(VipTableDataProvider.provideRegularList()));
    }

    private List<VipTableSimpleAdapter.CellItem> mapToPrivilegeItems(List<String> titles){
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        for (String t: titles){
            list.add(VipTableSimpleAdapter.CellItem.text(t, Color.parseColor("#333333")));
        }
        return list;
    }

    private void addCellToColumn(LinearLayout column, String text, TableRowData.RowType rowType, boolean isHeader, int columnType) {
        TextView tv = new TextView(this);
        tv.setText(text);
        tv.setGravity(Gravity.CENTER);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));
        tv.setLayoutParams(params);
        
        // 只为VIP和普通用户的标题行设置背景
        // columnType: 0=privilege, 1=super vip, 2=vip, 3=regular
        if (isHeader && columnType == 2) {
            // VIP列标题 - 粉红色背景
            tv.setBackgroundResource(R.drawable.bg_vip_column_red);
        } else if (isHeader && columnType == 3) {
            // 普通用户列标题 - 灰色背景
            tv.setBackgroundResource(R.drawable.bg_regular_column_gray);
        } else {
            tv.setBackgroundColor(Color.TRANSPARENT);
        }

        // 根据列类型设置文本颜色
        // columnType: 0=privilege, 1=super vip (yellow), 2=vip (red), 3=regular (gray)
        if (isHeader) {
            tv.setTextSize(14);
            tv.setTypeface(null, Typeface.BOLD);
            switch(columnType) {
                case 1: // Super VIP - golden/yellow text
                    tv.setTextColor(Color.parseColor("#AD8A49"));
                    break;
                case 2: // VIP - 白色文字（因为有红色背景）
                    tv.setTextColor(Color.WHITE);
                    break;
                default: // Privilege and Regular - dark text
                    tv.setTextColor(Color.parseColor("#333333"));
                    break;
            }
        } else {
            tv.setTextSize(12);
            switch(columnType) {
                case 1: // Super VIP - golden/yellow text
                    tv.setTextColor(Color.parseColor("#AD8A49"));
                    break;
                default: // 其他列 - gray text
                    tv.setTextColor(Color.parseColor("#999999"));
                    break;
            }
        }

        column.addView(tv);
    }

    private void addCellToColumn(LinearLayout column, String text, int iconRes, TableRowData.CellType cellType, TableRowData.RowType rowType, boolean isHeader, int columnType) {
        View cellView;
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));


        if (cellType == TableRowData.CellType.TEXT) {
            TextView tv = new TextView(this);
            tv.setText(text);
            tv.setGravity(Gravity.CENTER);
            tv.setLayoutParams(params);

            // 根据列类型设置文本颜色
            // columnType: 0=privilege, 1=super vip (yellow), 2=vip (red), 3=regular (gray)
            if (isHeader) {
                tv.setTextSize(14);
                tv.setTypeface(null, Typeface.BOLD);
                switch(columnType) {
                    case 1: // Super VIP - golden/yellow text
                        tv.setTextColor(Color.parseColor("#AD8A49"));
                        break;
                    case 2: // VIP - 白色文字（因为有红色背景）
                        tv.setTextColor(Color.WHITE);
                        break;
                    default: // Privilege and Regular - dark text
                        tv.setTextColor(Color.parseColor("#333333"));
                        break;
                }
            } else {
                tv.setTextSize(12);
                switch(columnType) {
                    case 1: // Super VIP - golden/yellow text
                        tv.setTextColor(Color.parseColor("#AD8A49"));
                        break;
                    default: // 其他列 - gray text
                        tv.setTextColor(Color.parseColor("#999999"));
                        break;
                }
            }
            cellView = tv;
        } else {
            LinearLayout container = new LinearLayout(this);
            container.setGravity(Gravity.CENTER);
            container.setLayoutParams(params);
            
            ImageView iv = new ImageView(this);
            iv.setImageResource(iconRes);
            LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(20), dpToPx(20));
            iv.setLayoutParams(iconParams);
            
            container.addView(iv);
            cellView = container;
        }

        // 只为VIP和普通用户的标题行设置背景
        // columnType: 0=privilege, 1=super vip, 2=vip, 3=regular
        if (isHeader && columnType == 2) {
            // VIP列标题 - 粉红色背景
            cellView.setBackgroundResource(R.drawable.bg_vip_column_red);
        } else if (isHeader && columnType == 3) {
            // 普通用户列标题 - 灰色背景
            cellView.setBackgroundResource(R.drawable.bg_regular_column_gray);
        } else {
            cellView.setBackgroundColor(Color.TRANSPARENT);
        }
        column.addView(cellView);
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**
     * 在不改变外层 CardView 宽度的前提下，精确调整某个内容区域的实际宽度。
     * 通过当前测量宽度进行增减，并限制在 [minWidth, parentWidth] 之间。
     */
    private void adjustViewWidth(View targetView, int deltaPx) {
        if (targetView == null) return;
        View parent = (View) targetView.getParent();
        if (parent == null) return;

        int parentWidth = parent.getWidth();
        if (parentWidth <= 0) {
            // 如果父视图尚未测量，延后到下一帧
            targetView.post(() -> adjustViewWidth(targetView, deltaPx));
            return;
        }

        int currentWidth = targetView.getWidth();
        if (currentWidth <= 0) {
            // 使用 LayoutParams 作为兜底（首次可能为 0dp）
            ViewGroup.LayoutParams lp = targetView.getLayoutParams();
            currentWidth = lp != null && lp.width > 0 ? lp.width : parentWidth;
        }

        // 设置最小宽度阈值，避免被无限缩小
        int minWidth = Math.max(targetView.getMinimumWidth(), dpToPx(150));
        int newWidth = Math.max(minWidth, Math.min(parentWidth, currentWidth + deltaPx));

        ViewGroup.LayoutParams layoutParams = targetView.getLayoutParams();
        if (layoutParams != null) {
            layoutParams.width = newWidth;
            targetView.setLayoutParams(layoutParams);
            targetView.requestLayout();
            targetView.invalidate();
        }
    }

    /**给标题栏的图标和文字着色
     * @param iconColor       图标颜色
     * @param rightTextColor  标题文字颜色
     */
    public void setIconAndTextAndBackgroundColor(int iconColor, int rightTextColor){
        toolbarUtil.setIconAndTextColor(iconColor, rightTextColor, Color.WHITE);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        if (mPresenter != null) {
            mPresenter.getData(true);
        }

        // 初始化时设置数字颜色
        restoreOriginalCountViewState();
    }

    public void onClick(View view){
        Log.d(TAG, "onClick id=" + view.getId());
        if(view.getId() == R.id.openGroupVipLayout){
            if(!MainAppUtils.checkLogin(BiguoVipOpenActivity.this)) return;
            BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
            String content = String.format(Locale.CHINA, "邀请1名新用户参团享半价开通折扣卡，开通后可享%.1f折优惠;24小时内未成团自动退款!", Float.parseFloat(bean.getDiscount()) * 10);

            if(mPresenter.getBiguoVipBean().getGroup_info() == null){
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent("点击确认即可免费获取会员权益！")
                        .setNegativeText("关闭")
                        .setPositiveText("免费获取")
                        .setPositiveClickListener(v -> {
                            // 直接调用支付成功方法，跳过支付流程
                            paySuccess();
                        })
                        .builder()
                        .show();
            }else {
                BiguoVipOpenBean.GroupInfo groupInfo = bean.getGroup_info();
                String shareUrl = groupInfo.getShare_url();
                if(!shareUrl.contains("?")){
                    shareUrl += "?";
                }
                shareUrl = shareUrl
                        + (shareUrl.endsWith("?") ? "" : "&")
                        + "group_id=" + groupInfo.getGroup_id()
                        + "&users_id=" + groupInfo.getUsers_id()
                        + "&inviter_id=" + groupInfo.getUsers_id();
                new ShareDialog.Builder(getActivity())
                        .setShareTitle("您有一个好友拼团福利待领取")
                        .setShareContent("新用户注册并加入拼团，立享半价解锁笔果折扣卡！")
                        .setShareUrl(shareUrl)
                        .setOnShareListener((type) -> showMessage("分享成功，请等待被邀请用户注册并参与拼团"))
                        .builder()
                        .show();
            }

        }else if(view.getId() == R.id.serviceAgreementView){
            // 不手动 toggle，避免与 CheckBox 默认点击切换重复，导致状态被“反复切换回去”
            HtmlActivity.start(this, Constant.AGREEMENT_USER3);

        }else if(view.getId() == R.id.ruleView){
            mPresenter.getRule("membership_rule");
        } else if (view.getId() == R.id.vip_info_layout) {
            if (isPurchaseComplete) return; // 如果购买已完成，则不响应切换
            Log.d(TAG, "click vip_info_layout");
            updateCardSelection(true);
            // 恢复价格为初始值
            TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
            if (danDuGouMaiMoney != null && originalSinglePriceText != null) danDuGouMaiMoney.setText(originalSinglePriceText);
            if (pinTuanGouMaiMoney != null && originalGroupPriceText != null) pinTuanGouMaiMoney.setText(originalGroupPriceText);

            // 恢复底部按钮原始状态
            restoreOriginalButtonState();

            // 恢复权益标题原始状态
            restoreOriginalCountViewState();
        } else if (view.getId() == R.id.discount_info_layout) {
            if (isPurchaseComplete) return; // 如果购买已完成，则不响应切换
            Log.d(TAG, "click discount_info_layout");
            updateCardSelection(false);
            // 点击折扣信息时更新价格显示
            TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
            TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setText("7.9");
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setText("4.9");

            // 更新底部按钮文案和显示
            binding.openGroupVipView.setText("¥5.90 立即购买");
            binding.openGroupSubView.setVisibility(View.GONE);

            // 更新权益标题文案和数字颜色
            TextView openCountView = binding.getRoot().findViewById(R.id.openCountView);
            if (openCountView != null) {
                String text = "折扣会员卡享受6大权益";
                SpannableString spannableString = new SpannableString(text);
                // 将数字"6"设置为红色
                int start = text.indexOf("6");
                if (start != -1) {
                    spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#D53E43")),
                                          start, start + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                openCountView.setText(spannableString);
            }

            // 立即同步整列表格背景
            updateTableHeaderBackground(false);
        } else if (view.getId() == R.id.singlePurchaseCard) {
            updatePurchaseCardWeight(true);
        } else if (view.getId() == R.id.groupPurchaseCard) {
            updatePurchaseCardWeight(false);
        }
    }

    @Override
    public void setDataSuccess(BiguoVipOpenBean bean) {
        //如果列表是空的，则创建一个数组
        if(AppUtil.isEmpty(bean.getEnjoy_discounts())){
            bean.setEnjoy_discounts(new ArrayList<>());
        }
        if(AppUtil.isEmpty(bean.getNot_enjoy_discounts())){
            bean.setNot_enjoy_discounts(new ArrayList<>());
        }

        // 顶部文案按“会员卡”规范展示
        binding.priceAndValidityView.setText("AI功能畅享用");
        try {
            // 右侧标题统一为“会员卡”而非“折扣会员卡”
            binding.discountView.setText("折扣会员卡");
        } catch (Exception e) {
            binding.discountView.setText("折扣会员卡");
        }
        // 右侧副标题
        binding.enjoyCountView.setText("课程折扣8.8折");

        // 处理拼团相关显示
        Log.d(TAG, "setDataSuccess: group_info = " + (bean.getGroup_info() == null ? "null" : "not null"));
        if(bean.getGroup_info() == null){
            binding.openGroupVipView.setText(String.format("¥%s 立即邀请", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            // 强制显示倒计时用于测试
            binding.countDownTimeView.setVisibility(View.VISIBLE);
            binding.countDownTimeView.setText("23:59:59"); // 设置默认倒计时显示
        }else {
            binding.openGroupVipView.setText(String.format("¥%s 拼团中", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            binding.countDownTimeView.setVisibility(View.VISIBLE);
        }
        // 表格标题文案：会员卡享受X大权益（数量后端可控，这里与设计保持一致）
        binding.openCountView.setText(String.format("折扣会员卡享受%s大权益", 8));

        // 顶部过期/成功容器已移除，直接保留购买UI

        // 真实用户状态判断
        try {
            UserBean userBean = UserCache.getUserCache();
            if (userBean != null) {
                Log.d(TAG, "setDataSuccess: user.membership=" + userBean.getMembership()
                        + ", expire=" + userBean.getMembership_expire());
                Log.d(TAG, "setDataSuccess: keep default purchase UI");
            }
        } catch (Exception ignore) {}

    }

    public int getDiscountsCount(List<BiguoVipOpenBean.DiscountsBean> list){
        int count = 0;
        for (int i=0;i<list.size(); i++){
            if(!AppUtil.isEmpty(list.get(i).getTitle())) {
                count ++;
            }
        }
        return count;
    }

    @Override
    public void refreshCountTime(long countTime) {
        binding.countDownTimeView.setText(TimeFormatUtils.formatMillisecond(countTime));
    }

    @Override
    public void showPayDialog(int isGroup, List<DiscountBean> allDiscount) {
        BiguoVipOpenBean openBean = mPresenter.getBiguoVipBean();
        String payPrice = isGroup == StartFinal.YES ? openBean.getGroup_price() : openBean.getPrice();
        new DiscountPayDialog.Builder(this, PayUtils.MEMBERSHIP)
                // 商品名称改为“会员卡”，与本页面定位一致
                .setGoodsName("折扣会员卡")
                .setShowGuobi(false)
                .setPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setPayPrice(Float.parseFloat(AppUtil.isEmpty(payPrice, "9999")))
                .setDiscountList(allDiscount)
                .setOnPayListener((joinGroup, discount, payType) -> {
                    int coupon_id = discount == null ? 0 : discount.getId();
                    mPresenter.payOrder(payType, 0, coupon_id, isGroup, openBean.getGroup_id());
                })
                .build()
                .show();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Override
    public void paySuccess() {
        Log.d(TAG, "paySuccess called in BiguoVipOpenActivity");
        isPurchaseComplete = true; // 标记购买已完成
        showMessage("支付成功");
        UserBean userBean = UserCache.getUserCache();
        Log.d(TAG, "paySuccess: before update - membership=" + userBean.getMembership() + ", is_super_vip=" + userBean.getIs_super_vip());
        userBean.setMembership(1);
        // 根据用户支付前选择的卡片，设置对应的会员类型
        userBean.setIs_super_vip(isSuperVipSelected ? "1" : "0");
        UserCache.cacheUser(userBean);
        Log.d(TAG, "paySuccess: after update - membership=" + userBean.getMembership() + ", is_super_vip=" + userBean.getIs_super_vip());
        
        // 发送会员开通成功事件，通知其他页面更新
        Log.d(TAG, "paySuccess: posting VIP_OPEN_SUCCESS event");
        EventBus.getDefault().post(true, EventBusTags.VIP_OPEN_SUCCESS);

        // 设置10秒后过期（仅本地调试，不影响服务端会员有效期字段）
        setDebugExpireAfterMs(10_000L);

        // 原地刷新UI，不再跳转到独立的成功页
        // 1. 刷新卡片和表格背景
        updateCardSelection(isSuperVipSelected);
        // 1.1 同步标题颜色（与 BiguoVipActivity 保持一致）
        syncTopCardTextColors(isSuperVipSelected);

        // 2. 隐藏购买按钮和勾选协议区域，防止重复操作
        if (binding.openGroupVipLayout != null) {
            binding.openGroupVipLayout.setVisibility(View.GONE);
        }
        if (binding.serviceAgreementView != null) {
            binding.serviceAgreementView.setVisibility(View.GONE);
        }
        // 可以选择性地显示一个成功提示，比如在原按钮位置显示"开通成功"
        binding.openGroupSubView.setText("开通成功，即将跳转");
        binding.openGroupSubView.setTextColor(Color.parseColor("#FE5656"));


        // 延迟跳转，让用户看到VIP成功状态
        binding.getRoot().postDelayed(() -> {
            ArmsUtils.startActivity(BiguoVipActivity.class);
            finish();
        }, 3000); // 3秒后跳转
    }

    private void syncTopCardTextColors(boolean isSuperVipSelected){
        TextView superVipTitle = binding.superVipTitle;
        TextView priceAndValidityView = binding.priceAndValidityView;
        TextView discountView = binding.discountView;
        TextView enjoyCountView = binding.enjoyCountView;
        int gold = Color.parseColor("#FFE9A3");
        int gray = Color.parseColor("#999999"); // 未选中的灰色
        if (isSuperVipSelected) {
            if (superVipTitle != null) superVipTitle.setTextColor(gold);
            if (priceAndValidityView != null) priceAndValidityView.setTextColor(gold);
            if (discountView != null) discountView.setTextColor(gray);
            if (enjoyCountView != null) enjoyCountView.setTextColor(gray);
        } else {
            if (superVipTitle != null) superVipTitle.setTextColor(gray);
            if (priceAndValidityView != null) priceAndValidityView.setTextColor(gray);
            if (discountView != null) discountView.setTextColor(gold);
            if (enjoyCountView != null) enjoyCountView.setTextColor(gold);
        }
    }

    @Override
    public void getRuleSuccess(List<String> rule) {
        new RuleDialog(this)
                .setTitleText("购买须知")
                .setRules(rule)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mPresenter !=null && mPresenter.getBiguoVipBean() != null) {
            mPresenter.getData(false);
        }
    }

    @Override
    public void showLoading() {
        // TODO: 实现加载中的UI，例如显示一个ProgressBar
    }

    @Override
    public void hideLoading() {
        // TODO: 隐藏加载中的UI
    }
    public void setToolBarErrorStyle(boolean isSuccess){
        if(isSuccess){
            setIconAndTextAndBackgroundColor(Color.WHITE, Color.WHITE);
        }else {
            setIconAndTextAndBackgroundColor(AppUtil.getColorRes(this, R.color.tblack), AppUtil.getColorRes(this, R.color.tblack));
        }
    }

    /**
     * 根据当前用户会员状态决定是否需要重定向。
     * 返回 true 表示已跳转并结束当前页。
     */
    private boolean routeByMembershipIfNeeded() {
        // 调试模式：强制显示未开通页面
        boolean DEBUG_FORCE_SHOW_OPEN_PAGE = true;
        if (DEBUG_FORCE_SHOW_OPEN_PAGE) {
            // 清除之前的调试过期时间
            clearDebugExpireAt();
            // 临时修改用户状态为未开通
            UserBean userBean = UserCache.getUserCache();
            if (userBean != null) {
                userBean.setMembership(0);
                UserCache.cacheUser(userBean);
            }
            return false; // 不跳转，显示当前页面
        }
        
        try {
            UserBean userBean = UserCache.getUserCache();
            if (userBean == null) return false;

            // 优先使用本地调试过期时间（按秒级精度）
            long debugExpireAt = getDebugExpireAtMs();
            long now = System.currentTimeMillis();
            if (debugExpireAt > 0) {
                if (now >= debugExpireAt) {
                    // 已超过调试过期时间，统一视为过期
                    if (userBean.getMembership() != 0) {
                        userBean.setMembership(0);
                        UserCache.cacheUser(userBean);
                    }
                    ArmsUtils.startActivity(BiguoVipExpiredActivity.class);
                    finish();
                    return true;
                } else if (userBean.getMembership() == 1) {
                    // 未到过期时间，且是会员 => 进入已开通页
                    ArmsUtils.startActivity(BiguoVipActivity.class);
                    finish();
                    return true;
                }
                // 若未到过期时间且非会员，则继续走后续逻辑（当作未开通过）
            }

            // 其次使用日期级别的服务端过期字段
            long expiredDays = computeExpiredDays(userBean.getMembership_expire());
            if (expiredDays > 0 || userBean.getMembership() == 0) {
                ArmsUtils.startActivity(BiguoVipExpiredActivity.class);
                finish();
                return true;
            }
            if (userBean.getMembership() == 1) {
                ArmsUtils.startActivity(BiguoVipActivity.class);
                finish();
                return true;
            }
        } catch (Exception ignore) {}
        return false;
    }

    /** 解析 yyyy-MM-dd 的过期日期，返回已过期的天数（未过期返回0） */
    private long computeExpiredDays(String expireDateString) {
        try {
            if (expireDateString == null || expireDateString.trim().length() == 0) return 0L;
            long expireSec = TimeFormatUtils.getSecondsFromDate("yyyy-MM-dd", expireDateString);
            if (expireSec <= 0) return 0L;
            long nowMs = System.currentTimeMillis();
            long expireMs = expireSec * 1000L;
            long diff = nowMs - expireMs;
            if (diff <= 0) return 0L;
            return java.util.concurrent.TimeUnit.MILLISECONDS.toDays(diff);
        } catch (Exception e) {
            return 0L;
        }
    }

    // ================== 调试用：10秒后过期 ==================
    private static final String PREF_VIP_DEBUG = "vip_debug_prefs";
    private static final String KEY_EXPIRE_AT_MS = "expire_at_ms";

    private void setDebugExpireAfterMs(long deltaMs) {
        SharedPreferences sp = getSharedPreferences(PREF_VIP_DEBUG, Context.MODE_PRIVATE);
        sp.edit().putLong(KEY_EXPIRE_AT_MS, System.currentTimeMillis() + deltaMs).apply();
    }

    private long getDebugExpireAtMs() {
        SharedPreferences sp = getSharedPreferences(PREF_VIP_DEBUG, Context.MODE_PRIVATE);
        return sp.getLong(KEY_EXPIRE_AT_MS, 0L);
    }

    private void clearDebugExpireAt() {
        SharedPreferences sp = getSharedPreferences(PREF_VIP_DEBUG, Context.MODE_PRIVATE);
        sp.edit().remove(KEY_EXPIRE_AT_MS).apply();
    }

    private void updateCardSelection(boolean isSuperVipSelected) {
        this.isSuperVipSelected = isSuperVipSelected; // 更新成员变量
        if (isSuperVipSelected) {
            // 选中超级会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#FFE9A3"));
            binding.priceAndValidityView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.discountView.setTextColor(Color.parseColor("#999999")); // 未选中的显示灰色
            binding.enjoyCountView.setTextColor(Color.parseColor("#999999")); // 未选中的显示灰色

            binding.leftGrainSuper.setVisibility(View.VISIBLE);
            binding.rightGrainSuper.setVisibility(View.VISIBLE);
            binding.triangleSuper.setVisibility(View.VISIBLE);

            binding.leftGrainDiscount.setVisibility(View.INVISIBLE);
            binding.rightGrainDiscount.setVisibility(View.INVISIBLE);
            binding.triangleDiscount.setVisibility(View.INVISIBLE);



        } else {
            // 选中折扣会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#999999")); // 未选中的显示灰色
            binding.priceAndValidityView.setTextColor(Color.parseColor("#999999")); // 未选中的显示灰色
            binding.discountView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.enjoyCountView.setTextColor(Color.parseColor("#FFE9A3"));

            binding.leftGrainSuper.setVisibility(View.INVISIBLE);
            binding.rightGrainSuper.setVisibility(View.INVISIBLE);
            binding.triangleSuper.setVisibility(View.INVISIBLE);

            binding.leftGrainDiscount.setVisibility(View.VISIBLE);
            binding.rightGrainDiscount.setVisibility(View.VISIBLE);
            binding.triangleDiscount.setVisibility(View.VISIBLE);


        }
        // 同步更新下方表格的列头背景：
        // isSuperVipSelected=true 显示黄色（超级会员）列头，隐藏 VIP 红色列头
        // isSuperVipSelected=false 隐藏黄色列头，显示 VIP 红色列头
        updateTableHeaderBackground(isSuperVipSelected);
        updatePurchaseCardWeight(true);
    }

    private void updateTableHeaderBackground(boolean isSuperVipSelected) {
        Log.d(TAG, "updateTableHeaderBackground isSuper=" + isSuperVipSelected);
        LinearLayout superVipColumn = binding.superVipColumn; // 自身列容器（含上下边框 bg_table_border_top_bottom）
        LinearLayout vipColumn = binding.vipColumn;           // 自身列容器（含上下边框 bg_table_border_top_bottom）

        // 顶部表头背景
        View superHeader = null;
        View vipHeader = null;
        if (superVipColumn != null && superVipColumn.getChildCount() > 0) {
            superHeader = superVipColumn.getChildAt(0);
        }
        if (vipColumn != null && vipColumn.getChildCount() > 0) {
            vipHeader = vipColumn.getChildAt(0);
        }

        // 叠加的背景遮罩视图（整列底色）：黄色与红色
        View yellowBackground = binding.getRoot().findViewById(R.id.background_yellow_column);
        View redBackground = binding.getRoot().findViewById(R.id.background_red_column);

        if (isSuperVipSelected) {
            // 点击 vip_info_layout：
            // 1) super_vip_column 的上下边框隐藏
            if (superVipColumn != null) superVipColumn.setBackgroundColor(Color.TRANSPARENT);
            // 2) super 表头黄色隐藏
            if (superHeader != null) superHeader.setBackgroundColor(Color.TRANSPARENT);
            // 3) 显示黄色遮罩，隐藏红色遮罩
            if (yellowBackground != null) yellowBackground.setVisibility(View.VISIBLE);
            if (redBackground != null) redBackground.setVisibility(View.GONE);
            Log.d(TAG, "bg -> yellow:VISIBLE, red:GONE");
            // 4) 保持 VIP 列容器边框与表头可见
            if (vipColumn != null) vipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);
        } else {
            // 点击 discount_info_layout：
            // 1) 隐藏黄色遮罩，显示红色遮罩
            if (yellowBackground != null) yellowBackground.setVisibility(View.GONE);
            if (redBackground != null) redBackground.setVisibility(View.VISIBLE);
            Log.d(TAG, "bg -> yellow:GONE, red:VISIBLE");
            // 2) 隐藏 VIP 列容器的上下边框
            if (vipColumn != null) vipColumn.setBackgroundColor(Color.TRANSPARENT);
            // 3) 保持 VIP 表头红色可见
            if (vipHeader != null) vipHeader.setBackgroundResource(R.drawable.bg_vip_card_header);
            // 4) 保持 super_vip_column 的上下边框可见，表头可按需显示柔黄
            if (superVipColumn != null) superVipColumn.setBackgroundResource(R.drawable.bg_table_border_top_bottom);
            if (superHeader != null) superHeader.setBackgroundResource(R.drawable.bg_super_vip_header);
        }

        // 顶部卡片麦穗与三角指示同步逻辑（与 BiguoVipActivity 保持一致）
        View leftGrainSuper = binding.getRoot().findViewById(R.id.leftGrainSuper);
        View rightGrainSuper = binding.getRoot().findViewById(R.id.rightGrainSuper);
        View leftGrainDiscount = binding.getRoot().findViewById(R.id.leftGrainDiscount);
        View rightGrainDiscount = binding.getRoot().findViewById(R.id.rightGrainDiscount);
        View triangleSuper = binding.getRoot().findViewById(R.id.triangleSuper);
        View triangleDiscount = binding.getRoot().findViewById(R.id.triangleDiscount);
        if (leftGrainSuper != null && rightGrainSuper != null && leftGrainDiscount != null && rightGrainDiscount != null && triangleSuper != null && triangleDiscount != null) {
            if (isSuperVipSelected) {
                leftGrainSuper.setVisibility(View.VISIBLE);
                rightGrainSuper.setVisibility(View.VISIBLE);
                triangleSuper.setVisibility(View.VISIBLE);
                leftGrainDiscount.setVisibility(View.INVISIBLE);
                rightGrainDiscount.setVisibility(View.INVISIBLE);
                triangleDiscount.setVisibility(View.INVISIBLE);
            } else {
                leftGrainSuper.setVisibility(View.INVISIBLE);
                rightGrainSuper.setVisibility(View.INVISIBLE);
                triangleSuper.setVisibility(View.INVISIBLE);
                leftGrainDiscount.setVisibility(View.VISIBLE);
                rightGrainDiscount.setVisibility(View.VISIBLE);
                triangleDiscount.setVisibility(View.VISIBLE);
            }
        }
    }

    private void updatePurchaseCardWeight(boolean isSinglePurchaseSelected) {
        final float targetWeight = 1.0f;
        final float normalWeight = 1.0f;
        final int duration = 300; // 动画时长

        LinearLayout.LayoutParams singleParams = (LinearLayout.LayoutParams) binding.singlePurchaseCard.getLayoutParams();
        LinearLayout.LayoutParams groupParams = (LinearLayout.LayoutParams) binding.groupPurchaseCard.getLayoutParams();

        float singleStartWeight = singleParams.weight;
        float groupStartWeight = groupParams.weight;

        float singleEndWeight = isSinglePurchaseSelected ? targetWeight : normalWeight;
        float groupEndWeight = isSinglePurchaseSelected ? normalWeight : targetWeight;

        // 为 singlePurchaseCard 创建动画
        ValueAnimator singleAnimator = ValueAnimator.ofFloat(singleStartWeight, singleEndWeight);
        singleAnimator.addUpdateListener(animation -> {
            singleParams.weight = (float) animation.getAnimatedValue();
            binding.singlePurchaseCard.setLayoutParams(singleParams);
        });
        singleAnimator.setDuration(duration);
        singleAnimator.start();

        // 为 groupPurchaseCard 创建动画
        ValueAnimator groupAnimator = ValueAnimator.ofFloat(groupStartWeight, groupEndWeight);
        groupAnimator.addUpdateListener(animation -> {
            groupParams.weight = (float) animation.getAnimatedValue();
            binding.groupPurchaseCard.setLayoutParams(groupParams);
        });
        groupAnimator.setDuration(duration);
        groupAnimator.start();

        // 获取相关的TextView
        TextView danDuGouMai = binding.getRoot().findViewById(R.id.dandugoumai);
        TextView danDuGouMaiMoney = binding.getRoot().findViewById(R.id.dandugoumai_money);
        TextView pMoney = binding.getRoot().findViewById(R.id.p_money);
        TextView pinTuanGouMai = binding.getRoot().findViewById(R.id.pingtuangoumai);
        TextView pinTuanGouMaiMoney = binding.getRoot().findViewById(R.id.pingtuangoumai_money);
        TextView ppMoney = binding.getRoot().findViewById(R.id.pp_money);

        int highlightColor = Color.parseColor("#FE5656");
        int normalColor = Color.parseColor("#333333");
        if (isSinglePurchaseSelected) {
            // 单独购买被选中
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);

            // 选中侧：单独购买三行文字高亮
            if (danDuGouMai != null) danDuGouMai.setTextColor(highlightColor);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(highlightColor);
            if (pMoney != null) pMoney.setTextColor(highlightColor);

            // 未选中侧：拼团三行文字默认
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(normalColor);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(normalColor);
            if (ppMoney != null) ppMoney.setTextColor(normalColor);
        } else {
            // 拼团购买被选中
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);

            // 未选中侧：单独购买三行文字默认
            if (danDuGouMai != null) danDuGouMai.setTextColor(normalColor);
            if (danDuGouMaiMoney != null) danDuGouMaiMoney.setTextColor(normalColor);
            if (pMoney != null) pMoney.setTextColor(normalColor);

            // 选中侧：拼团三行文字高亮
            if (pinTuanGouMai != null) pinTuanGouMai.setTextColor(highlightColor);
            if (pinTuanGouMaiMoney != null) pinTuanGouMaiMoney.setTextColor(highlightColor);
            if (ppMoney != null) ppMoney.setTextColor(highlightColor);
        }

        // 取消缩放动画，避免与宽度联动冲突
    }

    /**
     * 显示VIP成功卡片和权益表格
     */
    // 顶部过期/成功容器逻辑已移除

    

    /**
     * 计算距离过期的天数（已过期返回>0的天数，未过期返回0）。
     */
    

    /**
     * 恢复底部按钮原始状态
     */
    private void restoreOriginalButtonState() {
        BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
        if (bean != null) {
            // 恢复原始按钮文案
            if (bean.getGroup_info() == null) {
                binding.openGroupVipView.setText(String.format("¥%s 立即邀请", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
                binding.openGroupSubView.setText("邀请1名新用户参团");
            } else {
                binding.openGroupVipView.setText(String.format("¥%s 拼团中", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
                binding.openGroupSubView.setText("邀请1名新用户参团");
            }
            binding.openGroupSubView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 恢复权益标题原始状态
     */
    private void restoreOriginalCountViewState() {
        TextView openCountView = binding.getRoot().findViewById(R.id.openCountView);
        if (openCountView != null) {
            String text = "超级折扣会员卡享受8大权益";
            SpannableString spannableString = new SpannableString(text);
            // 将数字"8"设置为红色
            int start = text.indexOf("8");
            if (start != -1) {
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#D53E43")),
                                      start, start + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            openCountView.setText(spannableString);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    /**
     * 隐藏原有的购买相关UI
     */
    // 不再隐藏购买UI/表格

    /**
     * 初始化VIP成功卡片内容
     */
    // 过期/成功卡片内容初始化已删除

    
}
