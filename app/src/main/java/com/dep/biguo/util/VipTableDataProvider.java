package com.dep.biguo.util;

import android.graphics.Color;

import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.adapter.VipTableSimpleAdapter;

import java.util.ArrayList;
import java.util.List;

public class VipTableDataProvider {
    public static List<String> providePrivilegeList() {
        List<String> list = new ArrayList<>();
        list.add("购买VIP题库");
        list.add("购买考前押密");
        list.add("购买高频考点");
        list.add("购买精讲视频");
        list.add("购买串讲视频");
        list.add("购买直播密训");
        list.add("AI视频解析");
        list.add("AI文字解析");
        list.add("真题模拟");
        list.add("去除弹窗");
        list.add("专享交流群");
        list.add("学费减免");
        return list;
    }

    public static List<VipTableSimpleAdapter.CellItem> provideSuperVipList() {
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        int defaultGray = Color.parseColor("#999999");
        // 1-6 折扣（文本）
        for (int i = 0; i < 6; i++) list.add(VipTableSimpleAdapter.CellItem.text("8.8折", defaultGray));
        // AI视频/文字不限量
        list.add(VipTableSimpleAdapter.CellItem.text("使用不限量", defaultGray));
        list.add(VipTableSimpleAdapter.CellItem.text("使用不限量", defaultGray));
        // 真题模拟不限量
        list.add(VipTableSimpleAdapter.CellItem.text("生成不限量", defaultGray));
        // 去除弹窗/专享交流群/学费减免：统一改用图片图标
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_check));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_check));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_check));
        return list;
    }

    public static List<VipTableSimpleAdapter.CellItem> provideVipList() {
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        // 1-6 折扣
        for (int i = 0; i < 6; i++) list.add(VipTableSimpleAdapter.CellItem.text("8.8折", Color.parseColor("#999999")));
        // AI视频/文字 5次
        list.add(VipTableSimpleAdapter.CellItem.text("使用5次", Color.parseColor("#999999")));
        list.add(VipTableSimpleAdapter.CellItem.text("使用5次", Color.parseColor("#999999")));
        // 真题模拟/去除弹窗/专享交流群/学费减免：用图标
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_check));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_check));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        return list;
    }

    public static List<VipTableSimpleAdapter.CellItem> provideRegularList() {
        List<VipTableSimpleAdapter.CellItem> list = new ArrayList<>();
        // 1-6 原价
        for (int i = 0; i < 6; i++) list.add(VipTableSimpleAdapter.CellItem.text("原价", Color.parseColor("#999999")));
        // AI视频/文字 5次
        list.add(VipTableSimpleAdapter.CellItem.text("使用5次", Color.parseColor("#999999")));
        list.add(VipTableSimpleAdapter.CellItem.text("使用5次", Color.parseColor("#999999")));
        // 真题模拟/去除弹窗/专享交流群/学费减免：用图标
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        list.add(VipTableSimpleAdapter.CellItem.icon(R.drawable.ic_vip_cross));
        return list;
    }
}
