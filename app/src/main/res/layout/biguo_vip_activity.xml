<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.BiguoVipActivity" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/headLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_top_banner"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <FrameLayout
                        android:id="@+id/vipCardContainer"
                        android:layout_width="match_parent"
                        android:layout_height="190dp"
                        android:background="@android:color/transparent"
                        android:visibility="visible"
                        tools:visibility="visible"
                        app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent">
                        <include layout="@layout/item_vip_card_view" />
                    </FrameLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 调试：显示剩余过期倒计时（仅本地） -->
                <TextView
                    android:id="@+id/tv_debug_expire_countdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/headLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="剩余 8 秒自动过期"/>

                <!-- 表格容器：完全迁移开通页的实现（含背景层、白底遮罩与表格四列） -->

                <TextView
                    android:id="@+id/vipTitleView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="16dp"
                    android:drawablePadding="10dp"
                    android:text="当前已享受折扣会员卡特权"
                    android:textColor="@color/tblack"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.059"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_debug_expire_countdown" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="24dp"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/vipTitleView">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <!-- 背景列1：Super VIP 列（黄色） -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2"
                            android:layout_marginTop="0dp"
                            android:layout_marginBottom="0dp">
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"/>
                            <View
                                android:id="@+id/background_yellow_column"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.1"
                                android:background="@drawable/bg_yellow_column_with_border"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.9"/>
                        </LinearLayout>

                        <!-- 背景列2：VIP 列（红色） -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2"
                            android:layout_marginTop="0dp"
                            android:layout_marginBottom="0dp">
                            <!-- 左累积（特权+超级会员）1.2 + 1.1 = 2.3 -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="2.3"/>
                            <!-- VIP 列宽度 1.0 -->
                            <View
                                android:id="@+id/background_red_column"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.0"
                                android:background="@drawable/bg_red_column_with_border"
                                android:visibility="gone"/>
                            <!-- 右侧剩余（普通用户）0.9 -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.9"/>
                        </LinearLayout>

                        <!-- 白色背景区域 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2">

                            <!-- 左侧白色背景 -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"
                                android:background="@android:color/white"/>

                            <!-- Super VIP 列透明（让黄色显示） -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.1"/>

                            <!-- VIP 列透明（让红色显示） -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.0"/>

                            <!-- 右侧白色背景（普通用户） -->
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.9"
                                android:background="@android:color/white"/>
                        </LinearLayout>

                        <!-- 表格内容 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp">

                            <LinearLayout
                                android:id="@+id/vipTableLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:baselineAligned="false"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:divider="@null"
                                android:showDividers="none">

                                <!-- Privilege Column -->
                                <LinearLayout
                                    android:id="@+id/privilege_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1.2"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:background="@drawable/bg_table_border_left_side"
                                    android:padding="0dp">

                                    <!-- Header -->
                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_table_header"
                                        android:gravity="center"
                                        android:text="特权"
                                        android:textColor="#333333"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_privilege"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <!-- Super VIP Column -->
                                <LinearLayout
                                    android:id="@+id/super_vip_column"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.1"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_top_bottom"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_super_vip_header"
                                        android:gravity="center"
                                        android:text="超级会员卡"
                                        android:textColor="#FF9100"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_super_vip"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <!-- VIP Column -->
                                <LinearLayout
                                    android:id="@+id/vip_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_top_bottom"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_vip_card_header"
                                        android:gravity="center"
                                        android:text="会员卡"
                                        android:textColor="#FE5656"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_vip"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <!-- Regular User Column -->
                                <LinearLayout
                                    android:id="@+id/regular_user_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="0.9"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_right_corners"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_regular_user_header"
                                        android:gravity="center"
                                        android:text="普通用户"
                                        android:textColor="#333333"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_regular"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </FrameLayout>
                </FrameLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>