<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@+id/bottom_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 购买选项卡片区域（已过期样式） -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    xmlns:android="http://schemas.android.com/apk/res/android"
                    xmlns:app="http://schemas.android.com/apk/res-auto"
                    android:id="@+id/top_banner_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_top_banner"
                    android:minHeight="160dp"
                    android:paddingTop="24dp"
                    android:paddingBottom="4dp"
                    app:layout_constraintTop_toTopOf="parent">

                    <!-- 返回按钮 -->
                    <ImageView
                        android:id="@+id/backButton"
                        android:layout_width="7dp"
                        android:layout_height="14dp"
                        android:layout_marginStart="15dp"
                        android:src="@drawable/arrow_white_back"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 用户信息区域 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/userInfoContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="15dp"
                        android:layout_marginStart="15dp"
                        app:layout_constraintTop_toBottomOf="@+id/backButton">

                        <!-- 用户头像（圆角/圆形） -->
                        <com.dep.biguo.widget.CircleImageView
                            android:id="@+id/iv_avatar"
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:src="@drawable/app_icon"
                            android:contentDescription="用户头像"
                            app:civ_round_radius="30dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"/>

                        <!-- 用户昵称 -->
                        <TextView
                            android:id="@+id/tv_nickname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:layout_marginStart="12dp"
                            android:text="昵称最多展示九个字"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toEndOf="@id/iv_avatar"
                            app:layout_constraintTop_toTopOf="@id/iv_avatar" />

                        <!-- 会员过期提示 -->
                        <TextView
                            android:id="@+id/tv_expiry_message"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:layout_marginTop="4dp"
                            android:text="会员卡权益已过期21天"
                            android:textColor="#FFCCCCCC"
                            android:textSize="12sp"
                            app:layout_constraintStart_toEndOf="@id/iv_avatar"
                            app:layout_constraintTop_toBottomOf="@id/tv_nickname" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!-- 垂直中线，用于划分左右两列 -->
                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_vertical_50"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />

                    <!-- 左列：超级折扣会员卡 -->
                    <LinearLayout
                        android:id="@+id/vip_info_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="20dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toStartOf="@id/guideline_vertical_50"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/userInfoContainer">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/leftGrainSuper"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="3dp"
                                android:src="@drawable/left_grain" />

                            <TextView
                                android:id="@+id/superVipTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="超级折扣会员卡"
                                android:textColor="#FFFFFF"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/rightGrainSuper"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginStart="3dp"
                                android:src="@drawable/right_grain" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/priceAndValidityView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="AI功能畅享用"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp" />
                    </LinearLayout>

                <!-- 左列下方的选中状态小三角 -->
                <ImageView
                    android:id="@+id/triangleSuper"
                    android:layout_width="12dp"
                    android:layout_height="8dp"
                    android:layout_marginTop="23dp" 
                    android:layout_marginBottom="3dp"
                    android:src="@drawable/triangle"
                    app:layout_constraintTop_toBottomOf="@id/vip_info_layout"
                    app:layout_constraintBottom_toBottomOf="@id/top_banner_container"
                    app:layout_constraintVertical_bias="0"
                    app:layout_constraintEnd_toEndOf="@id/vip_info_layout"
                    app:layout_constraintStart_toStartOf="@id/vip_info_layout" />

                    <!-- 右列：折扣会员卡 -->
                    <LinearLayout
                        android:id="@+id/discount_info_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintTop_toTopOf="@id/vip_info_layout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/guideline_vertical_50">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <!-- 右侧麦穗（默认占位不可见） -->
                            <ImageView
                                android:id="@+id/leftGrainDiscount"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="3dp"
                                android:src="@drawable/left_grain"
                                android:visibility="invisible" />

                            <TextView
                                android:id="@+id/discountView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="折扣会员卡"
                                android:textColor="#FFFFFF"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <!-- 右侧麦穗（默认占位不可见） -->
                            <ImageView
                                android:id="@+id/rightGrainDiscount"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginStart="3dp"
                                android:src="@drawable/right_grain"
                                android:visibility="invisible" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/enjoyCountView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="课程折扣8.8折"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- 右列下方的小三角 (默认不选中，所以隐藏) -->
                    <ImageView
                        android:id="@+id/triangleDiscount"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_marginTop="23dp"
                        android:layout_marginBottom="3dp"
                        android:src="@drawable/triangle"
                        android:visibility="invisible"
                        app:layout_constraintTop_toBottomOf="@id/discount_info_layout"
                        app:layout_constraintBottom_toBottomOf="@id/top_banner_container"
                        app:layout_constraintVertical_bias="0"
                        app:layout_constraintEnd_toEndOf="@id/discount_info_layout"
                        app:layout_constraintStart_toStartOf="@id/discount_info_layout" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 原有两张购买卡片（单独购买/拼团购买）保持不变 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:orientation="horizontal"
                    android:paddingHorizontal="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:gravity="bottom"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/singlePurchaseCard"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="false"
                            app:cardUseCompatPadding="true">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:id="@+id/content_area"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:background="@drawable/bg_promo_tag_solidyellow"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical"
                                    android:paddingTop="20dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/dandugoumai"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="单独购买"
                                        android:textColor="@android:color/black"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:gravity="bottom"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/p_money"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥"
                                            android:textColor="@android:color/black"
                                            android:textSize="18sp" />

                                        <TextView
                                            android:id="@+id/dandugoumai_money"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="17.9"
                                            android:textColor="@android:color/black"
                                            android:textSize="32sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <TextView
                                        android:id="@+id/singleOriginalPrice"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="原价¥19.9"
                                        android:textColor="#9E9E9E"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="12dp"
                                        android:background="@drawable/bg_promo_tag_danyellow"
                                        android:gravity="center"
                                        android:paddingVertical="8dp"
                                        android:text="立省¥2"
                                        android:textColor="#FE5656" />
                                </LinearLayout>



                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </androidx.cardview.widget.CardView>

                        <View
                            android:layout_width="15dp"
                            android:layout_height="0dp" />

                        <androidx.cardview.widget.CardView
                            android:id="@+id/groupPurchaseCard"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="false"
                            app:cardUseCompatPadding="true">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <LinearLayout
                                    android:id="@+id/group_purchase_content_area"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:background="@drawable/bg_promo_tag_gradingyellows"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical"
                                    android:paddingTop="20dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:id="@+id/pingtuangoumai"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="拼团购买"
                                        android:textColor="#FE5656"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:gravity="bottom"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:id="@+id/pp_money"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥"
                                            android:textColor="#333333"
                                            android:textSize="18sp" />

                                        <TextView
                                            android:id="@+id/pingtuangoumai_money"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="14.9"
                                            android:textColor="#FE5656"
                                            android:textSize="32sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <TextView
                                        android:id="@+id/groupOriginalPrice"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="原价¥19.9"
                                        android:textColor="#9E9E9E"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="12dp"
                                        android:background="@drawable/bg_promo_tag_danyellow"
                                        android:gravity="center"
                                        android:paddingVertical="8dp"
                                        android:text="立省¥5"
                                        android:textColor="#E65100" />
                                </LinearLayout>
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>
                </LinearLayout>

                <!-- 会员权益表格（保持与开通页一致） -->
                <TextView
                    android:id="@+id/openCountView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="30dp"
                    android:text="超级折扣会员卡享受8大权益"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="24dp"
                    android:clipChildren="false"
                    android:clipToPadding="false">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2"
                            android:layout_marginTop="0dp"
                            android:layout_marginBottom="0dp">
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"/>
                            <View
                                android:id="@+id/background_yellow_column"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.1"
                                android:background="@drawable/bg_yellow_column_with_border"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.9"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2"
                            android:layout_marginTop="0dp"
                            android:layout_marginBottom="0dp">
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="2.3"/>
                            <View
                                android:id="@+id/background_red_column"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.0"
                                android:background="@drawable/bg_red_column_with_border"
                                android:visibility="gone"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.9"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:weightSum="4.2">
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"
                                android:background="@android:color/white"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.1"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="1.0"/>
                            <View
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.9"
                                android:background="@android:color/white"/>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp">

                            <LinearLayout
                                android:id="@+id/vipTableLayout"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:baselineAligned="false"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:divider="@null"
                                android:showDividers="none">

                                <LinearLayout
                                    android:id="@+id/privilege_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1.2"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:background="@drawable/bg_table_border_left_side"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_table_header"
                                        android:gravity="center"
                                        android:text="特权"
                                        android:textColor="#333333"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_privilege"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/super_vip_column"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.1"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_top_bottom"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_super_vip_header"
                                        android:gravity="center"
                                        android:text="超级会员卡"
                                        android:textColor="#FF9100"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_super_vip"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/vip_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_top_bottom"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_vip_card_header"
                                        android:gravity="center"
                                        android:text="会员卡"
                                        android:textColor="#FE5656"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_vip"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/regular_user_column"
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_weight="0.9"
                                    android:orientation="vertical"
                                    android:paddingVertical="5dp"
                                    android:gravity="center_horizontal"
                                    android:background="@drawable/bg_table_border_right_corners"
                                    android:padding="0dp">

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="46dp"
                                        android:background="@drawable/bg_regular_user_header"
                                        android:gravity="center"
                                        android:text="普通用户"
                                        android:textColor="#333333"
                                        android:textSize="14sp"
                                        android:textStyle="bold" />

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycler_regular"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:nestedScrollingEnabled="false"
                                        android:overScrollMode="never"
                                        android:scrollbars="none"/>
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </FrameLayout>
                </FrameLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:padding="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/openGroupVipLayout"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@drawable/bg_gradient_button_red"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/openGroupVipView"
                    style="@style/normalText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:text="¥12.9 立即拼团"
                    android:textColor="@android:color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/openGroupSubView"
                    style="@style/lightText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:alpha="1"
                    android:duplicateParentState="true"
                    android:text="邀请1名新用户"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <CheckBox
                android:id="@+id/serviceAgreementView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                app:buttonCompat="@drawable/login_check_button"
                android:checked="false"
                android:paddingStart="5dp"
                android:text=""
                android:textColor="#999999"
                android:textSize="12sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>


