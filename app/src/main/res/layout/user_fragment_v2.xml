<?xml version="1.0" encoding="utf-8"?>
<layout>
    <data>
        <variable
            name="onClickListenr"
            type="com.dep.biguo.mvp.ui.fragment.UserFragment" />
    </data>
    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:background="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/headLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/user_head_bg"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent">

                    <com.dep.biguo.widget.CircleImageView
                        android:id="@+id/avatarView"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="20dp"
                        android:onClick="@{onClickListenr.onClick}"
                        android:src="@drawable/default_avatar"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <ImageView
                        android:id="@+id/avatarBadgePlaceholderView"
                        android:layout_width="54dp"
                        android:layout_height="15dp"
                        android:visibility="invisible"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="@id/avatarView"
                        app:layout_constraintEnd_toEndOf="@id/avatarView"
                        app:layout_constraintBottom_toBottomOf="@id/avatarView"/>

                    <TextView
                        android:id="@+id/nikeNameView"
                        android:text="请登录"
                        android:textSize="18dp"
                        android:textColor="@color/twhite"
                        android:onClick="@{onClickListenr.onClick}"
                        android:drawablePadding="4dp"
                        android:layout_marginTop="4dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        app:layout_constraintStart_toEndOf="@id/avatarView"
                        app:layout_constraintTop_toTopOf="@id/avatarView"/>

                    <TextView
                        android:id="@+id/guobiView"
                        android:text="0"
                        android:textSize="12dp"
                        android:textColor="@color/twhite"
                        android:drawableStart="@drawable/user_guobi_icon"
                        android:drawablePadding="4dp"
                        android:layout_marginBottom="4dp"
                        android:onClick="@{onClickListenr.onClick}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="@id/nikeNameView"
                        app:layout_constraintBottom_toBottomOf="@id/avatarView"/>


                    <TextView
                        android:id="@+id/integralView"
                        android:text="0"
                        android:textSize="12dp"
                        android:textColor="@color/twhite"
                        android:drawableStart="@drawable/user_integral_icon"
                        android:drawablePadding="4dp"
                        android:layout_marginStart="10dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListenr.onClick}"
                        app:layout_constraintStart_toEndOf="@id/guobiView"
                        app:layout_constraintBottom_toBottomOf="@id/guobiView"/>


                    <TextView
                        android:id="@+id/dayCardView"
                        android:gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="26dp"
                        android:background="@drawable/user_recharge_v2_bg"
                        android:backgroundTint="@color/twhite"
                        android:text="每日打卡"
                        android:drawableStart="@drawable/user_day_card"
                        android:drawablePadding="4dp"
                        android:textColor="@color/theme"
                        android:textSize="12dp"
                        android:onClick="@{onClickListenr.onClick}"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        app:layout_constraintTop_toTopOf="@id/avatarView"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="@id/avatarView" />


                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/bonusView"
                        android:text="¥0\n奖金"
                        android:textSize="10dp"
                        android:textColor="@color/twhite"
                        android:gravity="center"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="42dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListenr.onClick}"
                        app:startChar="¥"
                        app:endChar="\n"
                        app:size="14dp"
                        app:layout_constraintStart_toStartOf="@id/avatarView"
                        app:layout_constraintTop_toBottomOf="@id/avatarView"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <View
                        android:id="@+id/line1View"
                        android:layout_width="1dp"
                        android:layout_height="0dp"
                        android:background="@color/twhite"
                        android:alpha="0.5"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        app:layout_constraintStart_toEndOf="@id/bonusView"
                        app:layout_constraintTop_toTopOf="@id/bonusView"
                        app:layout_constraintBottom_toBottomOf="@id/bonusView"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/answerCountView"
                        android:text="0\n答题数"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:gravity="center"
                        android:layout_marginStart="20dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListenr.onClick}"
                        app:startChar="\n"
                        app:size="10dp"
                        app:layout_constraintStart_toEndOf="@id/line1View"
                        app:layout_constraintTop_toTopOf="@id/bonusView"
                        app:layout_constraintEnd_toStartOf="@id/line2View"/>

                    <View
                        android:id="@+id/line2View"
                        android:layout_width="1dp"
                        android:layout_height="0dp"
                        android:background="@color/twhite"
                        android:alpha="0.5"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="6dp"
                        android:layout_marginBottom="6dp"
                        app:layout_constraintStart_toEndOf="@id/answerCountView"
                        app:layout_constraintTop_toTopOf="@id/answerCountView"
                        app:layout_constraintBottom_toBottomOf="@id/answerCountView"/>

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/correctRoteView"
                        android:text="0%\n正确率"
                        android:textSize="14dp"
                        android:textColor="@color/twhite"
                        android:gravity="center"
                        android:layout_marginStart="20dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:startChar="\n"
                        app:size="10dp"
                        app:layout_constraintStart_toEndOf="@id/line2View"
                        app:layout_constraintTop_toTopOf="@id/answerCountView"/>

                    <LinearLayout
                        android:id="@+id/countDownTimeLayout"
                        android:gravity="center"
                        android:layout_marginEnd="30dp"
                        android:orientation="vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:onClick="@{onClickListenr.onClick}"
                        app:layout_constraintTop_toTopOf="@id/correctRoteView"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="@id/correctRoteView">

                        <com.dep.biguo.widget.DiversificationTextView
                            android:id="@+id/countDownTimeView"
                            android:text="0天"
                            android:textSize="14dp"
                            android:textColor="@color/twhite"
                            android:gravity="center"
                            android:drawableEnd="@drawable/down_white_arrow"
                            android:drawablePadding="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>

                        <TextView
                            android:text="考试倒计时"
                            android:textColor="@color/twhite"
                            android:textSize="12dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>

                    <!--user_biguo_vip_go_open_bg这个图片是.9图片，
                    在运行时，不在拉伸范围内的部分会变成边距，效果等同内边距-->
                    <LinearLayout
                        android:id="@+id/biguoVipLayout"
                        android:layout_width="match_parent"
                        android:layout_height="46dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="20dp"
                        android:gravity="center_vertical"
                        android:onClick="@{onClickListenr.onClick}"
                        android:background="@mipmap/user_biguo_vip_go_open_bg_new"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/bonusView">
                        <TextView
                            android:id="@+id/biguoVipTextView"
                            android:text="开通会员卡享受6大权益, 题库最高立省¥864"
                            android:textSize="13dp"
                            android:textColor="#FFE9A3"
                            android:layout_marginStart="10dp"
                            android:drawableStart="@drawable/user_biguo_go_open_icon_new"
                            android:layout_weight="1"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:ellipsize="end"/>

                        <TextView
                            android:id="@+id/openBiguoVipView"
                            android:text="立即开通"
                            android:textSize="14dp"
                            android:textColor="#525050"
                            android:gravity="center"
                            android:layout_marginEnd="10dp"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:background="@drawable/user_biguo_vip_go_open"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"/>
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>


                <com.biguo.utils.widget.StyleConstraintLayout
                    android:id="@+id/taskCenterLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="6dp"
                    android:onClick="@{onClickListenr.onClick}"
                    app:bgGradientStartColor="@color/white"
                    app:shadowColor="@color/gray_gradient_1_bg"
                    app:shadowWidth="8dp"
                    app:all_round="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/headLayout"
                    app:layout_constraintEnd_toStartOf="@id/invitationFriendLayout">

                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/taskCenterView"
                        android:text="任务中心\n做任务可领积分"
                        android:textSize="14dp"
                        android:textColor="@color/tblack"
                        android:gravity="center_vertical"
                        android:drawableEnd="@drawable/user_task_center"
                        android:drawablePadding="6dp"
                        android:paddingStart="8dp"
                        android:paddingTop="16dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="16dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:startChar="\n"
                        app:size="12dp"
                        app:changeColor="@color/black3"/>
                </com.biguo.utils.widget.StyleConstraintLayout>

                <com.biguo.utils.widget.StyleConstraintLayout
                    android:id="@+id/invitationFriendLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:onClick="@{onClickListenr.onClick}"
                    app:bgGradientStartColor="@color/white"
                    app:shadowColor="@color/gray_gradient_1_bg"
                    app:shadowWidth="8dp"
                    app:all_round="10dp"
                    app:layout_constraintStart_toEndOf="@id/taskCenterLayout"
                    app:layout_constraintTop_toTopOf="@id/taskCenterLayout"
                    app:layout_constraintEnd_toEndOf="parent">
                    <com.dep.biguo.widget.DiversificationTextView
                        android:id="@+id/invitationFriendView"
                        android:text="邀请好友\n邀请好友赚奖金"
                        android:textSize="14dp"
                        android:textColor="@color/tblack"
                        android:gravity="center_vertical"
                        android:drawableEnd="@drawable/user_invitation_friend"
                        android:drawablePadding="6dp"
                        android:paddingStart="8dp"
                        android:paddingTop="16dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="16dp"
                        android:elevation="2dp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:startChar="\n"
                        app:size="12dp"
                        app:changeColor="@color/black3"/>
                </com.biguo.utils.widget.StyleConstraintLayout>


                <View
                    android:id="@+id/line4View"
                    android:background="@color/line"
                    android:layout_marginTop="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    app:layout_constraintTop_toBottomOf="@id/taskCenterLayout"/>

                <TextView
                    android:id="@+id/courseTitleView"
                    android:text="课程相关"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line4View"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/courseRecyclerView"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="20dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/courseTitleView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:spanCount="4"
                    tools:itemCount="4"
                    tools:listitem="@layout/icon_item"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"/>

                <View
                    android:id="@+id/line5View"
                    android:background="@color/line"
                    android:layout_marginTop="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    app:layout_constraintTop_toBottomOf="@id/courseRecyclerView"/>

                <TextView
                    android:id="@+id/serviceTitleView"
                    android:text="我的服务"
                    android:textSize="16dp"
                    android:textColor="@color/tblack"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line5View"/>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/serviceRecyclerView"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:paddingBottom="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/serviceTitleView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:spanCount="4"
                    tools:itemCount="10"
                    tools:listitem="@layout/icon_item"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"/>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </FrameLayout>
</layout>

